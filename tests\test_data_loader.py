"""Unit tests for DataLoader module."""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import patch, Mock

from src.data_loader import DataLoader


class TestDataLoader:
    """Test cases for DataLoader class."""
    
    def test_init(self, temp_excel_file):
        """Test DataLoader initialization."""
        loader = DataLoader(temp_excel_file)
        assert loader.excel_path == temp_excel_file
        assert isinstance(loader.raw_data, dict)
        assert isinstance(loader.normalized_data, dict)
        assert isinstance(loader.schema_info, dict)
    
    def test_clean_column_name(self, data_loader):
        """Test column name cleaning."""
        test_cases = [
            ('Product Name', 'product_name'),
            ('Order-ID', 'order_id'),
            ('Customer.Email', 'customer_email'),
            ('Price ($)', 'price'),
            ('  Spaced  Name  ', 'spaced_name'),
            ('123Number', '123number'),
            ('UPPERCASE', 'uppercase')
        ]
        
        for input_name, expected in test_cases:
            result = data_loader._clean_column_name(input_name)
            assert result == expected
    
    def test_load_excel_data(self, data_loader, sample_excel_data):
        """Test Excel data loading."""
        loaded_data = data_loader.load_excel_data()
        
        # Check that all expected sheets are loaded
        expected_sheets = set(sample_excel_data.keys())
        loaded_sheets = set(loaded_data.keys())
        assert expected_sheets == loaded_sheets
        
        # Check data integrity
        for sheet_name in expected_sheets:
            assert len(loaded_data[sheet_name]) > 0
            assert isinstance(loaded_data[sheet_name], pd.DataFrame)
    
    def test_load_excel_data_file_not_found(self):
        """Test Excel loading with non-existent file."""
        loader = DataLoader("non_existent_file.xlsx")
        
        with pytest.raises(FileNotFoundError):
            loader.load_excel_data()
    
    def test_validate_data_types(self, data_loader, sample_excel_data):
        """Test data type validation."""
        data_loader.raw_data = sample_excel_data
        validated_data = data_loader.validate_data_types()
        
        # Check that data types are properly converted
        assert 'categories' in validated_data
        categories_df = validated_data['categories']
        assert categories_df['categoryid'].dtype == 'int64'
        assert categories_df['categoryname'].dtype == 'string'
        
        # Check orders date conversion
        if 'orders' in validated_data:
            orders_df = validated_data['orders']
            assert pd.api.types.is_datetime64_any_dtype(orders_df['orderdate'])
    
    def test_handle_missing_values(self, data_loader):
        """Test missing value handling."""
        # Create test data with missing values
        test_data = {
            'customers': pd.DataFrame({
                'customerid': ['ALFKI', 'ANATR', None],
                'companyname': ['Company A', 'Company B', 'Company C'],
                'region': ['North', None, 'South'],
                'fax': ['123-456', None, '789-012']
            }),
            'products': pd.DataFrame({
                'productid': [1, 2, 3],
                'productname': ['Product A', 'Product B', 'Product C'],
                'discontinued': [False, None, True],
                'unitsinstock': [10, None, 5]
            })
        }
        
        data_loader.raw_data = test_data
        cleaned_data = data_loader.handle_missing_values()
        
        # Check that missing values are handled appropriately
        customers_df = cleaned_data['customers']
        assert customers_df['region'].isna().sum() == 0  # Should be filled with 'Unknown'
        assert len(customers_df) == 2  # Row with None customerid should be removed
        
        products_df = cleaned_data['products']
        assert products_df['discontinued'].isna().sum() == 0  # Should be filled with False
        assert products_df['unitsinstock'].isna().sum() == 0  # Should be filled with 0
    
    def test_remove_duplicates(self, data_loader):
        """Test duplicate removal."""
        # Create test data with duplicates
        test_data = {
            'customers': pd.DataFrame({
                'customerid': ['ALFKI', 'ALFKI', 'ANATR'],
                'companyname': ['Company A', 'Company A', 'Company B'],
                'city': ['Berlin', 'Berlin', 'Mexico']
            }),
            'products': pd.DataFrame({
                'productid': [1, 1, 2, 3],
                'productname': ['Product A', 'Product A', 'Product B', 'Product C'],
                'unitprice': [10.0, 10.0, 15.0, 20.0]
            })
        }
        
        data_loader.raw_data = test_data
        deduplicated_data = data_loader.remove_duplicates()
        
        # Check that duplicates are removed
        customers_df = deduplicated_data['customers']
        assert len(customers_df) == 2  # One duplicate removed
        assert customers_df['customerid'].nunique() == 2
        
        products_df = deduplicated_data['products']
        assert len(products_df) == 3  # One duplicate removed
        assert products_df['productid'].nunique() == 3
    
    def test_validate_referential_integrity(self, data_loader, sample_excel_data):
        """Test referential integrity validation."""
        # Add invalid foreign key references
        invalid_data = sample_excel_data.copy()
        invalid_data['orders'] = pd.DataFrame({
            'orderid': [10248, 10249, 10250],
            'customerid': ['ALFKI', 'INVALID', 'ANTON'],  # INVALID doesn't exist
            'orderdate': pd.to_datetime(['1996-07-04', '1996-07-05', '1996-07-08']),
            'freight': [32.38, 11.61, 65.83]
        })
        
        data_loader.raw_data = invalid_data
        validated_data = data_loader.validate_referential_integrity()
        
        # Check that invalid references are removed
        orders_df = validated_data['orders']
        valid_customers = set(invalid_data['customers']['customerid'])
        for customer_id in orders_df['customerid']:
            if pd.notna(customer_id):
                assert customer_id in valid_customers
    
    def test_normalize_to_3nf(self, data_loader, sample_excel_data):
        """Test 3NF normalization."""
        data_loader.raw_data = sample_excel_data
        normalized_data = data_loader.normalize_to_3nf()
        
        # Check that audit timestamps are added
        for table_name, df in normalized_data.items():
            assert 'created_at' in df.columns
            assert 'updated_at' in df.columns
            assert not df['created_at'].isna().any()
            assert not df['updated_at'].isna().any()
        
        # Check that primary keys have correct data types
        if 'categories' in normalized_data:
            assert normalized_data['categories']['categoryid'].dtype == 'int64'
        if 'customers' in normalized_data:
            assert normalized_data['customers']['customerid'].dtype == 'string'
    
    def test_generate_schema_ddl(self, data_loader):
        """Test DDL generation."""
        ddl = data_loader.generate_schema_ddl()
        
        # Check that DDL contains expected elements
        assert 'CREATE TABLE' in ddl
        assert 'PRIMARY KEY' in ddl
        assert 'REFERENCES' in ddl
        assert 'CHECK' in ddl
        assert 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP' in ddl
        
        # Check for specific tables
        expected_tables = ['categories', 'customers', 'products', 'orders', 'order_details']
        for table in expected_tables:
            assert f'CREATE TABLE {table}' in ddl
    
    def test_generate_indexes_ddl(self, data_loader):
        """Test index DDL generation."""
        indexes_ddl = data_loader.generate_indexes_ddl()
        
        # Check that index DDL contains expected elements
        assert 'CREATE INDEX' in indexes_ddl
        assert 'idx_orders_customerid' in indexes_ddl
        assert 'idx_products_categoryid' in indexes_ddl
        assert 'idx_orders_orderdate' in indexes_ddl
    
    @patch('src.data_loader.Config.get_schema_path')
    def test_run_full_pipeline(self, mock_get_schema_path, data_loader, sample_excel_data):
        """Test complete normalization pipeline."""
        # Mock schema path
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as tmp_file:
            mock_get_schema_path.return_value = tmp_file.name
            
            # Set up test data
            data_loader.raw_data = sample_excel_data
            
            # Run pipeline
            result = data_loader.run_full_pipeline()
            
            # Check that result contains normalized data
            assert isinstance(result, dict)
            assert len(result) > 0
            
            # Check that schema file was created
            assert os.path.exists(tmp_file.name)
            
            # Check file content
            with open(tmp_file.name, 'r') as f:
                content = f.read()
                assert 'CREATE TABLE' in content
                assert 'CREATE INDEX' in content
            
            # Cleanup
            os.unlink(tmp_file.name)
    
    def test_log_normalization_metrics(self, data_loader, sample_excel_data, caplog):
        """Test normalization metrics logging."""
        data_loader.normalized_data = sample_excel_data
        
        with caplog.at_level('INFO'):
            data_loader._log_normalization_metrics()
        
        # Check that metrics are logged
        assert 'NORMALIZATION METRICS' in caplog.text
        assert 'Number of tables:' in caplog.text
        assert 'Total rows across all tables:' in caplog.text
    
    def test_empty_data_handling(self, data_loader):
        """Test handling of empty data."""
        data_loader.raw_data = {}
        
        # Should handle empty data gracefully
        result = data_loader.normalize_to_3nf()
        assert isinstance(result, dict)
        assert len(result) == 0
    
    def test_invalid_excel_format(self):
        """Test handling of invalid Excel format."""
        # Create a text file with .xlsx extension
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False) as tmp_file:
            tmp_file.write("This is not an Excel file")
            tmp_file.flush()
            
            loader = DataLoader(tmp_file.name)
            
            with pytest.raises(Exception):  # Should raise some kind of exception
                loader.load_excel_data()
            
            # Cleanup
            os.unlink(tmp_file.name)
