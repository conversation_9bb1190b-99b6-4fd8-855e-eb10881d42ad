"""Text2SQL Analytics System - Production-ready Text to SQL conversion with PostgreSQL."""

__version__ = "1.0.0"
__author__ = "Makebell Inc. Backend Engineer Assessment"
__email__ = "<EMAIL>"

from .config import Config
from .database import DatabaseManager
from .text2sql_engine import Text2SQLEngine
from .query_validator import QueryValidator
from .data_loader import DataLoader

__all__ = [
    "Config",
    "DatabaseManager", 
    "Text2SQLEngine",
    "QueryValidator",
    "DataLoader",
]
