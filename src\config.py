"""Configuration management for Text2SQL Analytics System."""

import os
import logging
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for managing environment variables and settings."""
    
    # Database Configuration
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: int = int(os.getenv("DB_PORT", "5432"))
    DB_NAME: str = os.getenv("DB_NAME", "text2sql_analytics")
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "")
    DB_URL: str = os.getenv("DB_URL", f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")
    
    # Read-only database user for query execution
    DB_QUERY_USER: str = os.getenv("DB_QUERY_USER", "admin")
    DB_QUERY_PASSWORD: str = os.getenv("DB_QUERY_PASSWORD", "")

    @classmethod
    def get_query_url(cls) -> str:
        """Get the query database URL with proper formatting."""
        return f"postgresql://{cls.DB_QUERY_USER}:{cls.DB_QUERY_PASSWORD}@{cls.DB_HOST}:{cls.DB_PORT}/{cls.DB_NAME}"
    
    # Google Gemini API Configuration
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    
    # Application Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    QUERY_TIMEOUT: int = int(os.getenv("QUERY_TIMEOUT", "5"))
    MAX_ROWS: int = int(os.getenv("MAX_ROWS", "1000"))
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    
    # Connection Pool Settings
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", "5"))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", "10"))
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that all required configuration values are present."""
        required_vars = [
            ("DB_PASSWORD", cls.DB_PASSWORD),
            ("GEMINI_API_KEY", cls.GEMINI_API_KEY),
        ]
        
        missing_vars = []
        for var_name, var_value in required_vars:
            if not var_value:
                missing_vars.append(var_name)
        
        if missing_vars:
            logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            return False
        
        return True
    
    @classmethod
    def setup_logging(cls) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, cls.LOG_LEVEL.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('text2sql.log')
            ]
        )
    
    @classmethod
    def get_data_path(cls, filename: str) -> str:
        """Get the full path to a data file."""
        return os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "raw", filename)
    
    @classmethod
    def get_schema_path(cls, filename: str) -> str:
        """Get the full path to a schema file."""
        return os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "schema", filename)
