"""Tests for Text2SQL engine functionality."""

import pytest
from unittest.mock import Mock, patch, MagicMock
import time
from functools import lru_cache

from src.text2sql_engine import Text2SQLEngine
from src.database import DatabaseManager
from src.query_validator import QueryValidator


class TestText2SQLEngine:
    """Test Text2SQLEngine class functionality."""
    
    @pytest.fixture
    def mock_database_manager(self):
        """Mock DatabaseManager."""
        db_manager = Mock(spec=DatabaseManager)
        db_manager.execute_query.return_value = ([{'id': 1, 'name': 'test'}], 1)
        db_manager.get_table_info.return_value = [
            ('customerid', 'VARCHAR(5)', 'NO', 'PRI', None, ''),
            ('companyname', 'VARCHAR(40)', 'NO', '', None, ''),
            ('country', 'VARCHAR(15)', 'YES', '', None, '')
        ]
        return db_manager
    
    @pytest.fixture
    def mock_query_validator(self):
        """Mock QueryValidator."""
        validator = Mock(spec=QueryValidator)
        validator.validate_query.return_value = (True, "SELECT * FROM customers", None)
        return validator
    
    @pytest.fixture
    def mock_gemini_model(self):
        """Mock Google Gemini model."""
        model = Mock()
        response = Mock()
        response.text = "SELECT * FROM customers WHERE country = 'Germany'"
        model.generate_content.return_value = response
        return model
    
    @pytest.fixture
    def text2sql_engine(self, mock_database_manager, mock_query_validator):
        """Text2SQLEngine instance with mocked dependencies."""
        with patch('src.text2sql_engine.genai.GenerativeModel') as mock_model_class:
            mock_model = Mock()
            mock_model_class.return_value = mock_model
            
            engine = Text2SQLEngine(mock_database_manager)
            engine.query_validator = mock_query_validator
            engine.model = mock_model
            
            return engine
    
    def test_init_with_database_manager(self, mock_database_manager):
        """Test Text2SQLEngine initialization with database manager."""
        with patch('src.text2sql_engine.genai.GenerativeModel'):
            engine = Text2SQLEngine(mock_database_manager)
            
            assert engine.db_manager == mock_database_manager
            assert engine.query_validator is not None
            assert engine.model is not None
    
    def test_init_without_database_manager(self):
        """Test Text2SQLEngine initialization without database manager."""
        with patch('src.text2sql_engine.genai.GenerativeModel'):
            with patch('src.text2sql_engine.DatabaseManager') as mock_db_class:
                mock_db = Mock()
                mock_db_class.return_value = mock_db
                
                engine = Text2SQLEngine()
                
                assert engine.db_manager == mock_db
                mock_db_class.assert_called_once_with(use_query_user=True)
    
    def test_get_schema_info_success(self, text2sql_engine, mock_database_manager):
        """Test successful schema information retrieval."""
        # Mock table list
        mock_database_manager.execute_query.return_value = (
            [{'table_name': 'customers'}, {'table_name': 'products'}], 2
        )
        
        # Mock table info for each table
        mock_database_manager.get_table_info.side_effect = [
            [('customerid', 'VARCHAR(5)', 'NO', 'PRI', None, '')],
            [('productid', 'INTEGER', 'NO', 'PRI', None, '')]
        ]
        
        schema_info = text2sql_engine.get_schema_info()
        
        assert 'customers' in schema_info
        assert 'products' in schema_info
        assert len(schema_info['customers']) == 1
        assert schema_info['customers'][0][0] == 'customerid'
    
    def test_get_schema_info_cached(self, text2sql_engine, mock_database_manager):
        """Test that schema info is cached."""
        # First call
        mock_database_manager.execute_query.return_value = (
            [{'table_name': 'customers'}], 1
        )
        mock_database_manager.get_table_info.return_value = [
            ('customerid', 'VARCHAR(5)', 'NO', 'PRI', None, '')
        ]
        
        schema1 = text2sql_engine.get_schema_info()
        schema2 = text2sql_engine.get_schema_info()
        
        # Should only call database once due to caching
        assert mock_database_manager.execute_query.call_count == 1
        assert schema1 == schema2
    
    def test_create_prompt_basic(self, text2sql_engine):
        """Test basic prompt creation."""
        with patch.object(text2sql_engine, 'get_schema_info') as mock_schema:
            mock_schema.return_value = {
                'customers': [('customerid', 'VARCHAR(5)', 'NO', 'PRI', None, '')]
            }
            
            question = "Show all customers"
            prompt = text2sql_engine._create_prompt(question)
            
            assert question in prompt
            assert 'customers' in prompt
            assert 'customerid' in prompt
            assert 'PostgreSQL' in prompt
    
    def test_create_prompt_with_relationships(self, text2sql_engine):
        """Test prompt creation includes relationship information."""
        with patch.object(text2sql_engine, 'get_schema_info') as mock_schema:
            mock_schema.return_value = {
                'customers': [('customerid', 'VARCHAR(5)', 'NO', 'PRI', None, '')],
                'orders': [('orderid', 'INTEGER', 'NO', 'PRI', None, ''),
                          ('customerid', 'VARCHAR(5)', 'YES', 'MUL', None, '')]
            }
            
            question = "Show customer orders"
            prompt = text2sql_engine._create_prompt(question)
            
            assert 'customers' in prompt
            assert 'orders' in prompt
            assert 'customerid' in prompt  # Foreign key relationship
    
    def test_generate_sql_success(self, text2sql_engine):
        """Test successful SQL generation."""
        question = "Show customers from Germany"
        expected_sql = "SELECT * FROM customers WHERE country = 'Germany'"
        
        # Mock Gemini response
        text2sql_engine.model.generate_content.return_value.text = expected_sql
        
        generated_sql = text2sql_engine.generate_sql(question)
        
        assert generated_sql == expected_sql
        text2sql_engine.model.generate_content.assert_called_once()
    
    def test_generate_sql_with_caching(self, text2sql_engine):
        """Test SQL generation uses caching."""
        question = "Show all customers"
        expected_sql = "SELECT * FROM customers"
        
        text2sql_engine.model.generate_content.return_value.text = expected_sql
        
        # Generate same question twice
        sql1 = text2sql_engine.generate_sql(question)
        sql2 = text2sql_engine.generate_sql(question)
        
        assert sql1 == sql2
        # Should only call Gemini once due to caching
        assert text2sql_engine.model.generate_content.call_count == 1
    
    def test_generate_sql_api_error(self, text2sql_engine):
        """Test SQL generation with API error."""
        question = "Show all customers"
        
        # Mock API error
        text2sql_engine.model.generate_content.side_effect = Exception("API Error")
        
        with pytest.raises(Exception) as exc_info:
            text2sql_engine.generate_sql(question)
        
        assert "API Error" in str(exc_info.value)
    
    def test_generate_sql_cleans_response(self, text2sql_engine):
        """Test SQL generation cleans Gemini response."""
        question = "Show customers"
        
        # Mock response with extra formatting
        messy_response = """
        ```sql
        SELECT * FROM customers;
        ```
        """
        text2sql_engine.model.generate_content.return_value.text = messy_response
        
        cleaned_sql = text2sql_engine.generate_sql(question)
        
        assert cleaned_sql == "SELECT * FROM customers"
        assert '```' not in cleaned_sql
        assert cleaned_sql.strip() == cleaned_sql
    
    def test_execute_question_success(self, text2sql_engine, mock_database_manager, mock_query_validator):
        """Test successful question execution."""
        question = "Show customers from Germany"
        generated_sql = "SELECT * FROM customers WHERE country = 'Germany'"
        
        # Mock SQL generation
        text2sql_engine.model.generate_content.return_value.text = generated_sql
        
        # Mock validation
        mock_query_validator.validate_query.return_value = (True, generated_sql, None)
        
        # Mock database execution
        mock_results = [{'customerid': 'ALFKI', 'companyname': 'Alfreds Futterkiste'}]
        mock_database_manager.execute_query.return_value = (mock_results, 1)
        
        result = text2sql_engine.execute_question(question)
        
        assert result['success'] is True
        assert result['sql'] == generated_sql
        assert result['results'] == mock_results
        assert result['row_count'] == 1
        assert result['error'] is None
        assert 'execution_time' in result
    
    def test_execute_question_validation_failure(self, text2sql_engine, mock_query_validator):
        """Test question execution with validation failure."""
        question = "DROP TABLE customers"
        dangerous_sql = "DROP TABLE customers"
        
        # Mock SQL generation
        text2sql_engine.model.generate_content.return_value.text = dangerous_sql
        
        # Mock validation failure
        mock_query_validator.validate_query.return_value = (False, None, "Dangerous query detected")
        
        result = text2sql_engine.execute_question(question)
        
        assert result['success'] is False
        assert result['sql'] == dangerous_sql
        assert result['results'] == []
        assert result['error'] == "Dangerous query detected"
    
    def test_execute_question_database_error(self, text2sql_engine, mock_database_manager, mock_query_validator):
        """Test question execution with database error."""
        question = "Show customers"
        sql = "SELECT * FROM customers"
        
        # Mock SQL generation and validation
        text2sql_engine.model.generate_content.return_value.text = sql
        mock_query_validator.validate_query.return_value = (True, sql, None)
        
        # Mock database error
        mock_database_manager.execute_query.side_effect = Exception("Database connection failed")
        
        result = text2sql_engine.execute_question(question)
        
        assert result['success'] is False
        assert result['sql'] == sql
        assert result['results'] == []
        assert "Database connection failed" in result['error']
    
    def test_execute_question_with_explanation(self, text2sql_engine, mock_database_manager, mock_query_validator):
        """Test question execution with explanation."""
        question = "Show customers from Germany"
        sql = "SELECT * FROM customers WHERE country = 'Germany'"
        
        # Mock successful execution
        text2sql_engine.model.generate_content.return_value.text = sql
        mock_query_validator.validate_query.return_value = (True, sql, None)
        mock_database_manager.execute_query.return_value = ([], 0)
        
        result = text2sql_engine.execute_question(question, explain=True)
        
        assert result['success'] is True
        assert 'explanation' in result
        assert result['explanation'] is not None
    
    def test_explain_query_success(self, text2sql_engine):
        """Test query explanation generation."""
        sql = "SELECT * FROM customers WHERE country = 'Germany'"
        
        # Mock explanation response
        explanation = "This query selects all customers from Germany"
        text2sql_engine.model.generate_content.return_value.text = explanation
        
        result = text2sql_engine.explain_query(sql)
        
        assert result == explanation
        text2sql_engine.model.generate_content.assert_called()
    
    def test_explain_query_error(self, text2sql_engine):
        """Test query explanation with error."""
        sql = "SELECT * FROM customers"
        
        # Mock API error
        text2sql_engine.model.generate_content.side_effect = Exception("API Error")
        
        result = text2sql_engine.explain_query(sql)
        
        assert "Error generating explanation" in result
    
    def test_get_cache_info(self, text2sql_engine):
        """Test cache information retrieval."""
        # Generate some cached queries
        text2sql_engine.model.generate_content.return_value.text = "SELECT * FROM customers"
        text2sql_engine.generate_sql("Show customers")
        text2sql_engine.generate_sql("List customers")
        
        cache_info = text2sql_engine.get_cache_info()
        
        assert 'hits' in cache_info
        assert 'misses' in cache_info
        assert 'maxsize' in cache_info
        assert 'currsize' in cache_info
        assert 'hit_rate' in cache_info
    
    def test_clear_cache(self, text2sql_engine):
        """Test cache clearing."""
        # Generate cached query
        text2sql_engine.model.generate_content.return_value.text = "SELECT * FROM customers"
        text2sql_engine.generate_sql("Show customers")
        
        # Clear cache
        text2sql_engine.clear_cache()
        
        # Cache should be empty
        cache_info = text2sql_engine.get_cache_info()
        assert cache_info['currsize'] == 0
    
    def test_performance_timing(self, text2sql_engine, mock_database_manager, mock_query_validator):
        """Test that execution time is measured."""
        question = "Show customers"
        sql = "SELECT * FROM customers"
        
        # Mock components
        text2sql_engine.model.generate_content.return_value.text = sql
        mock_query_validator.validate_query.return_value = (True, sql, None)
        
        # Add delay to database execution
        def slow_execute(*args, **kwargs):
            time.sleep(0.1)
            return ([], 0)
        
        mock_database_manager.execute_query.side_effect = slow_execute
        
        result = text2sql_engine.execute_question(question)
        
        assert result['execution_time'] >= 0.1
        assert result['execution_time'] < 1.0  # Should be reasonable
    
    def test_concurrent_requests(self, text2sql_engine, mock_database_manager, mock_query_validator):
        """Test handling of concurrent requests."""
        import threading
        
        question = "Show customers"
        sql = "SELECT * FROM customers"
        
        # Mock components
        text2sql_engine.model.generate_content.return_value.text = sql
        mock_query_validator.validate_query.return_value = (True, sql, None)
        mock_database_manager.execute_query.return_value = ([], 0)
        
        results = []
        
        def execute_question():
            result = text2sql_engine.execute_question(question)
            results.append(result)
        
        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=execute_question)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # All should succeed
        assert len(results) == 5
        assert all(result['success'] for result in results)
    
    def test_sql_cleaning_edge_cases(self, text2sql_engine):
        """Test SQL cleaning handles edge cases."""
        test_cases = [
            ("```sql\nSELECT * FROM customers;\n```", "SELECT * FROM customers"),
            ("SELECT * FROM customers;", "SELECT * FROM customers"),
            ("  SELECT * FROM customers  ", "SELECT * FROM customers"),
            ("```\nSELECT * FROM customers\n```", "SELECT * FROM customers"),
            ("", ""),
            ("   ", ""),
        ]
        
        for input_sql, expected_output in test_cases:
            text2sql_engine.model.generate_content.return_value.text = input_sql
            result = text2sql_engine.generate_sql("test question")
            assert result == expected_output
