"""Data normalization pipeline for Northwind dataset."""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import re
from datetime import datetime

from .config import Config

logger = logging.getLogger(__name__)


class DataLoader:
    """Handles loading, validation, and normalization of Northwind Excel data."""
    
    def __init__(self, excel_path: Optional[str] = None):
        """Initialize DataLoader with path to Excel file."""
        self.excel_path = excel_path or Config.get_data_path("northwind.xlsx")
        self.raw_data: Dict[str, pd.DataFrame] = {}
        self.normalized_data: Dict[str, pd.DataFrame] = {}
        self.schema_info: Dict[str, Dict] = {}
        
    def load_excel_data(self) -> Dict[str, pd.DataFrame]:
        """Load all sheets from Northwind Excel file."""
        try:
            logger.info(f"Loading Excel data from {self.excel_path}")
            
            # Read all sheets from Excel file
            excel_file = pd.ExcelFile(self.excel_path)
            
            for sheet_name in excel_file.sheet_names:
                logger.info(f"Loading sheet: {sheet_name}")
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                # Clean column names (remove spaces, special chars)
                df.columns = [self._clean_column_name(col) for col in df.columns]
                
                self.raw_data[sheet_name.lower()] = df
                logger.info(f"Loaded {len(df)} rows from {sheet_name}")
            
            logger.info(f"Successfully loaded {len(self.raw_data)} sheets")
            return self.raw_data
            
        except FileNotFoundError:
            logger.error(f"Excel file not found: {self.excel_path}")
            raise
        except Exception as e:
            logger.error(f"Error loading Excel data: {str(e)}")
            raise
    
    def _clean_column_name(self, column_name: str) -> str:
        """Clean column names for database compatibility."""
        # Convert to lowercase, replace spaces and special chars with underscores
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', str(column_name).lower())
        # Remove multiple consecutive underscores
        clean_name = re.sub(r'_+', '_', clean_name)
        # Remove leading/trailing underscores
        clean_name = clean_name.strip('_')
        return clean_name
    
    def validate_data_types(self) -> Dict[str, pd.DataFrame]:
        """Validate and convert data types for each table."""
        logger.info("Starting data type validation")
        
        # Define expected data types for key columns
        type_mappings = {
            'categories': {
                'categoryid': 'int64',
                'categoryname': 'string',
                'description': 'string'
            },
            'customers': {
                'customerid': 'string',
                'companyname': 'string',
                'contactname': 'string',
                'contacttitle': 'string',
                'address': 'string',
                'city': 'string',
                'region': 'string',
                'postalcode': 'string',
                'country': 'string',
                'phone': 'string',
                'fax': 'string'
            },
            'employees': {
                'employeeid': 'int64',
                'lastname': 'string',
                'firstname': 'string',
                'title': 'string',
                'titleofcourtesy': 'string',
                'birthdate': 'datetime64[ns]',
                'hiredate': 'datetime64[ns]',
                'address': 'string',
                'city': 'string',
                'region': 'string',
                'postalcode': 'string',
                'country': 'string',
                'homephone': 'string',
                'extension': 'string',
                'notes': 'string',
                'reportsto': 'Int64'  # Nullable integer
            },
            'orders': {
                'orderid': 'int64',
                'customerid': 'string',
                'employeeid': 'Int64',
                'orderdate': 'datetime64[ns]',
                'requireddate': 'datetime64[ns]',
                'shippeddate': 'datetime64[ns]',
                'shipvia': 'Int64',
                'freight': 'float64',
                'shipname': 'string',
                'shipaddress': 'string',
                'shipcity': 'string',
                'shipregion': 'string',
                'shippostalcode': 'string',
                'shipcountry': 'string'
            },
            'order_details': {
                'orderid': 'int64',
                'productid': 'int64',
                'unitprice': 'float64',
                'quantity': 'int64',
                'discount': 'float64'
            },
            'products': {
                'productid': 'int64',
                'productname': 'string',
                'supplierid': 'Int64',
                'categoryid': 'Int64',
                'quantityperunit': 'string',
                'unitprice': 'float64',
                'unitsinstock': 'Int64',
                'unitsonorder': 'Int64',
                'reorderlevel': 'Int64',
                'discontinued': 'bool'
            },
            'suppliers': {
                'supplierid': 'int64',
                'companyname': 'string',
                'contactname': 'string',
                'contacttitle': 'string',
                'address': 'string',
                'city': 'string',
                'region': 'string',
                'postalcode': 'string',
                'country': 'string',
                'phone': 'string',
                'fax': 'string',
                'homepage': 'string'
            },
            'shippers': {
                'shipperid': 'int64',
                'companyname': 'string',
                'phone': 'string'
            }
        }
        
        validated_data = {}
        
        for table_name, df in self.raw_data.items():
            logger.info(f"Validating data types for {table_name}")
            
            if table_name in type_mappings:
                df_copy = df.copy()
                
                for column, dtype in type_mappings[table_name].items():
                    if column in df_copy.columns:
                        try:
                            if dtype.startswith('datetime'):
                                df_copy[column] = pd.to_datetime(df_copy[column], errors='coerce')
                            elif dtype == 'bool':
                                df_copy[column] = df_copy[column].astype(bool)
                            else:
                                df_copy[column] = df_copy[column].astype(dtype)
                        except Exception as e:
                            logger.warning(f"Could not convert {column} to {dtype}: {str(e)}")
                
                validated_data[table_name] = df_copy
            else:
                validated_data[table_name] = df
        
        self.raw_data = validated_data
        logger.info("Data type validation completed")
        return validated_data

    def handle_missing_values(self) -> Dict[str, pd.DataFrame]:
        """Handle missing values and NULL constraints."""
        logger.info("Handling missing values")

        cleaned_data = {}

        for table_name, df in self.raw_data.items():
            df_copy = df.copy()

            # Handle specific missing value strategies per table
            if table_name == 'employees':
                # Fill missing regions with 'Unknown'
                df_copy['region'] = df_copy['region'].fillna('Unknown')
                # Reports_to can be NULL (top-level employees)

            elif table_name == 'customers':
                # Fill missing regions and fax with appropriate defaults
                df_copy['region'] = df_copy['region'].fillna('Unknown')
                df_copy['fax'] = df_copy['fax'].fillna('')

            elif table_name == 'orders':
                # Shipped date can be NULL for unshipped orders
                # Fill missing freight with 0
                df_copy['freight'] = df_copy['freight'].fillna(0.0)
                df_copy['shipregion'] = df_copy['shipregion'].fillna('Unknown')

            elif table_name == 'products':
                # Handle discontinued flag
                df_copy['discontinued'] = df_copy['discontinued'].fillna(False)
                # Units in stock, on order can be 0
                df_copy['unitsinstock'] = df_copy['unitsinstock'].fillna(0)
                df_copy['unitsonorder'] = df_copy['unitsonorder'].fillna(0)
                df_copy['reorderlevel'] = df_copy['reorderlevel'].fillna(0)

            elif table_name == 'suppliers':
                df_copy['region'] = df_copy['region'].fillna('Unknown')
                df_copy['fax'] = df_copy['fax'].fillna('')
                df_copy['homepage'] = df_copy['homepage'].fillna('')

            # Remove rows with critical missing values (primary keys)
            if table_name == 'categories' and 'categoryid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['categoryid'])
            elif table_name == 'customers' and 'customerid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['customerid'])
            elif table_name == 'employees' and 'employeeid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['employeeid'])
            elif table_name == 'orders' and 'orderid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['orderid'])
            elif table_name == 'products' and 'productid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['productid'])
            elif table_name == 'suppliers' and 'supplierid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['supplierid'])
            elif table_name == 'shippers' and 'shipperid' in df_copy.columns:
                df_copy = df_copy.dropna(subset=['shipperid'])

            cleaned_data[table_name] = df_copy
            logger.info(f"Cleaned {table_name}: {len(df)} -> {len(df_copy)} rows")

        self.raw_data = cleaned_data
        return cleaned_data

    def remove_duplicates(self) -> Dict[str, pd.DataFrame]:
        """Detect and remove duplicate records."""
        logger.info("Removing duplicates")

        deduplicated_data = {}

        for table_name, df in self.raw_data.items():
            initial_count = len(df)

            # Define primary key columns for deduplication
            pk_columns = {
                'categories': ['categoryid'],
                'customers': ['customerid'],
                'employees': ['employeeid'],
                'orders': ['orderid'],
                'order_details': ['orderid', 'productid'],
                'products': ['productid'],
                'suppliers': ['supplierid'],
                'shippers': ['shipperid']
            }

            if table_name in pk_columns:
                # Remove duplicates based on primary key
                df_dedup = df.drop_duplicates(subset=pk_columns[table_name], keep='first')
            else:
                # Remove complete duplicates
                df_dedup = df.drop_duplicates(keep='first')

            duplicates_removed = initial_count - len(df_dedup)
            if duplicates_removed > 0:
                logger.warning(f"Removed {duplicates_removed} duplicates from {table_name}")

            deduplicated_data[table_name] = df_dedup

        self.raw_data = deduplicated_data
        return deduplicated_data

    def validate_referential_integrity(self) -> Dict[str, pd.DataFrame]:
        """Ensure referential integrity between tables."""
        logger.info("Validating referential integrity")

        # Define foreign key relationships
        foreign_keys = {
            'orders': [
                ('customerid', 'customers', 'customerid'),
                ('employeeid', 'employees', 'employeeid'),
                ('shipvia', 'shippers', 'shipperid')
            ],
            'order_details': [
                ('orderid', 'orders', 'orderid'),
                ('productid', 'products', 'productid')
            ],
            'products': [
                ('supplierid', 'suppliers', 'supplierid'),
                ('categoryid', 'categories', 'categoryid')
            ],
            'employees': [
                ('reportsto', 'employees', 'employeeid')
            ]
        }

        validated_data = self.raw_data.copy()

        for table_name, fk_list in foreign_keys.items():
            if table_name not in validated_data:
                continue

            df = validated_data[table_name]
            initial_count = len(df)

            for fk_col, ref_table, ref_col in fk_list:
                if fk_col not in df.columns or ref_table not in validated_data:
                    continue

                ref_df = validated_data[ref_table]
                if ref_col not in ref_df.columns:
                    continue

                # Get valid reference values (excluding NaN)
                valid_refs = set(ref_df[ref_col].dropna().unique())

                # Filter out rows with invalid foreign keys (but allow NaN for nullable FKs)
                if fk_col in ['reportsto', 'employeeid', 'supplierid', 'categoryid', 'shipvia']:
                    # These can be NULL
                    mask = df[fk_col].isna() | df[fk_col].isin(valid_refs)
                else:
                    # These cannot be NULL
                    mask = df[fk_col].isin(valid_refs)

                df = df[mask]

                invalid_count = initial_count - len(df)
                if invalid_count > 0:
                    logger.warning(f"Removed {invalid_count} rows from {table_name} due to invalid {fk_col}")

                initial_count = len(df)

            validated_data[table_name] = df

        self.raw_data = validated_data
        return validated_data

    def normalize_to_3nf(self) -> Dict[str, pd.DataFrame]:
        """Normalize data to Third Normal Form (3NF)."""
        logger.info("Normalizing data to 3NF")

        normalized_data = self.raw_data.copy()

        # The Northwind dataset is already well-normalized, but let's ensure 3NF
        # and add audit timestamps

        for table_name, df in normalized_data.items():
            df_copy = df.copy()

            # Add audit timestamps
            current_time = datetime.now()
            df_copy['created_at'] = current_time
            df_copy['updated_at'] = current_time

            # Ensure proper data types for primary keys
            if table_name == 'categories' and 'categoryid' in df_copy.columns:
                df_copy['categoryid'] = df_copy['categoryid'].astype('int64')
            elif table_name == 'customers' and 'customerid' in df_copy.columns:
                df_copy['customerid'] = df_copy['customerid'].astype('string')
            elif table_name == 'employees' and 'employeeid' in df_copy.columns:
                df_copy['employeeid'] = df_copy['employeeid'].astype('int64')
            elif table_name == 'orders' and 'orderid' in df_copy.columns:
                df_copy['orderid'] = df_copy['orderid'].astype('int64')
            elif table_name == 'products' and 'productid' in df_copy.columns:
                df_copy['productid'] = df_copy['productid'].astype('int64')
            elif table_name == 'suppliers' and 'supplierid' in df_copy.columns:
                df_copy['supplierid'] = df_copy['supplierid'].astype('int64')
            elif table_name == 'shippers' and 'shipperid' in df_copy.columns:
                df_copy['shipperid'] = df_copy['shipperid'].astype('int64')

            normalized_data[table_name] = df_copy

        self.normalized_data = normalized_data
        logger.info("Data normalization completed")
        return normalized_data

    def generate_schema_ddl(self) -> str:
        """Generate PostgreSQL DDL for the normalized schema."""
        logger.info("Generating PostgreSQL schema DDL")

        ddl_statements = []

        # Add header comment
        ddl_statements.append("-- Northwind Database Schema")
        ddl_statements.append("-- Generated automatically from normalized data")
        ddl_statements.append("-- Supports 3NF with proper constraints and indexes")
        ddl_statements.append("")

        # Create tables in dependency order
        table_definitions = {
            'categories': """
CREATE TABLE categories (
    categoryid SERIAL PRIMARY KEY,
    categoryname VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'suppliers': """
CREATE TABLE suppliers (
    supplierid SERIAL PRIMARY KEY,
    companyname VARCHAR(255) NOT NULL,
    contactname VARCHAR(255),
    contacttitle VARCHAR(255),
    address VARCHAR(255),
    city VARCHAR(255),
    region VARCHAR(255),
    postalcode VARCHAR(20),
    country VARCHAR(255),
    phone VARCHAR(50),
    fax VARCHAR(50),
    homepage TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'shippers': """
CREATE TABLE shippers (
    shipperid SERIAL PRIMARY KEY,
    companyname VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'customers': """
CREATE TABLE customers (
    customerid VARCHAR(10) PRIMARY KEY,
    companyname VARCHAR(255) NOT NULL,
    contactname VARCHAR(255),
    contacttitle VARCHAR(255),
    address VARCHAR(255),
    city VARCHAR(255),
    region VARCHAR(255),
    postalcode VARCHAR(20),
    country VARCHAR(255),
    phone VARCHAR(50),
    fax VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'employees': """
CREATE TABLE employees (
    employeeid SERIAL PRIMARY KEY,
    lastname VARCHAR(255) NOT NULL,
    firstname VARCHAR(255) NOT NULL,
    title VARCHAR(255),
    titleofcourtesy VARCHAR(50),
    birthdate DATE,
    hiredate DATE,
    address VARCHAR(255),
    city VARCHAR(255),
    region VARCHAR(255),
    postalcode VARCHAR(20),
    country VARCHAR(255),
    homephone VARCHAR(50),
    extension VARCHAR(10),
    notes TEXT,
    reportsto INTEGER REFERENCES employees(employeeid),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'products': """
CREATE TABLE products (
    productid SERIAL PRIMARY KEY,
    productname VARCHAR(255) NOT NULL,
    supplierid INTEGER REFERENCES suppliers(supplierid),
    categoryid INTEGER REFERENCES categories(categoryid),
    quantityperunit VARCHAR(255),
    unitprice DECIMAL(10,2) CHECK (unitprice >= 0),
    unitsinstock INTEGER CHECK (unitsinstock >= 0),
    unitsonorder INTEGER CHECK (unitsonorder >= 0),
    reorderlevel INTEGER CHECK (reorderlevel >= 0),
    discontinued BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'orders': """
CREATE TABLE orders (
    orderid SERIAL PRIMARY KEY,
    customerid VARCHAR(10) REFERENCES customers(customerid),
    employeeid INTEGER REFERENCES employees(employeeid),
    orderdate DATE,
    requireddate DATE,
    shippeddate DATE,
    shipvia INTEGER REFERENCES shippers(shipperid),
    freight DECIMAL(10,2) CHECK (freight >= 0),
    shipname VARCHAR(255),
    shipaddress VARCHAR(255),
    shipcity VARCHAR(255),
    shipregion VARCHAR(255),
    shippostalcode VARCHAR(20),
    shipcountry VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);""",

            'order_details': """
CREATE TABLE order_details (
    orderid INTEGER REFERENCES orders(orderid) ON DELETE CASCADE,
    productid INTEGER REFERENCES products(productid),
    unitprice DECIMAL(10,2) NOT NULL CHECK (unitprice >= 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    discount DECIMAL(4,3) CHECK (discount >= 0 AND discount <= 1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (orderid, productid)
);"""
        }

        # Add table definitions
        for table_name, ddl in table_definitions.items():
            ddl_statements.append(ddl)
            ddl_statements.append("")

        return "\n".join(ddl_statements)

    def generate_indexes_ddl(self) -> str:
        """Generate DDL for database indexes."""
        logger.info("Generating index DDL")

        index_statements = [
            "-- Indexes for performance optimization",
            "",
            "-- Primary key indexes are created automatically",
            "",
            "-- Foreign key indexes for join performance",
            "CREATE INDEX idx_orders_customerid ON orders(customerid);",
            "CREATE INDEX idx_orders_employeeid ON orders(employeeid);",
            "CREATE INDEX idx_orders_shipvia ON orders(shipvia);",
            "CREATE INDEX idx_order_details_orderid ON order_details(orderid);",
            "CREATE INDEX idx_order_details_productid ON order_details(productid);",
            "CREATE INDEX idx_products_supplierid ON products(supplierid);",
            "CREATE INDEX idx_products_categoryid ON products(categoryid);",
            "CREATE INDEX idx_employees_reportsto ON employees(reportsto);",
            "",
            "-- Date indexes for time-based queries",
            "CREATE INDEX idx_orders_orderdate ON orders(orderdate);",
            "CREATE INDEX idx_orders_shippeddate ON orders(shippeddate);",
            "CREATE INDEX idx_employees_hiredate ON employees(hiredate);",
            "",
            "-- Text search indexes",
            "CREATE INDEX idx_customers_companyname ON customers(companyname);",
            "CREATE INDEX idx_products_productname ON products(productname);",
            "CREATE INDEX idx_suppliers_companyname ON suppliers(companyname);",
            "",
            "-- Composite indexes for common query patterns",
            "CREATE INDEX idx_orders_customer_date ON orders(customerid, orderdate);",
            "CREATE INDEX idx_products_category_price ON products(categoryid, unitprice);",
            ""
        ]

        return "\n".join(index_statements)

    def run_full_pipeline(self) -> Dict[str, pd.DataFrame]:
        """Run the complete data normalization pipeline."""
        logger.info("Starting full data normalization pipeline")

        try:
            # Step 1: Load Excel data
            self.load_excel_data()

            # Step 2: Validate data types
            self.validate_data_types()

            # Step 3: Handle missing values
            self.handle_missing_values()

            # Step 4: Remove duplicates
            self.remove_duplicates()

            # Step 5: Validate referential integrity
            self.validate_referential_integrity()

            # Step 6: Normalize to 3NF
            self.normalize_to_3nf()

            # Step 7: Generate schema DDL
            schema_ddl = self.generate_schema_ddl()
            indexes_ddl = self.generate_indexes_ddl()

            # Save schema to file
            schema_path = Config.get_schema_path("schema.sql")
            with open(schema_path, 'w', encoding='utf-8') as f:
                f.write(schema_ddl)
                f.write("\n")
                f.write(indexes_ddl)

            logger.info(f"Schema DDL saved to {schema_path}")

            # Log normalization metrics
            self._log_normalization_metrics()

            logger.info("Data normalization pipeline completed successfully")
            return self.normalized_data

        except Exception as e:
            logger.error(f"Data normalization pipeline failed: {str(e)}")
            raise

    def _log_normalization_metrics(self) -> None:
        """Log metrics about the normalization process."""
        logger.info("=== NORMALIZATION METRICS ===")
        logger.info(f"Number of tables: {len(self.normalized_data)}")

        total_rows = 0
        for table_name, df in self.normalized_data.items():
            row_count = len(df)
            total_rows += row_count
            logger.info(f"{table_name}: {row_count} rows")

        logger.info(f"Total rows across all tables: {total_rows}")
        logger.info("Data normalized to 3NF with:")
        logger.info("- Primary keys defined")
        logger.info("- Foreign key constraints")
        logger.info("- Check constraints for data integrity")
        logger.info("- Indexes for query optimization")
        logger.info("- Audit timestamps")
        logger.info("===============================")
