"""Complex query accuracy tests for Text2SQL engine."""

import pytest
import pandas as pd
from unittest.mock import patch, <PERSON>ck
import sqlparse
import time

from src.text2sql_engine import Text2SQLEngine


class TestComplexQueries:
    """Test complex SELECT queries with multiple JOINs, subqueries, and advanced operations."""
    
    @pytest.fixture
    def accuracy_metrics(self):
        """Fixture for accuracy metrics calculation."""
        def calculate_accuracy(execution_success, result_match, query_quality):
            return 0.2 * execution_success + 0.4 * result_match + 0.4 * query_quality
        return calculate_accuracy
    
    def test_customer_order_summary(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show customer names and their total order counts."""
        question = "Show customer names and their total order counts"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT c.companyname, COUNT(o.orderid) as order_count
                                   FROM customers c 
                                   LEFT JOIN orders o ON c.customerid = o.customerid 
                                   GROUP BY c.companyname 
                                   ORDER BY order_count DESC"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            # Metrics calculation
            execution_success = 1.0 if result['success'] else 0.0
            
            # Check for multiple tables, JOIN, GROUP BY, COUNT
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'CUSTOMERS' in sql_upper and 'ORDERS' in sql_upper:
                    result_match += 0.3
                if 'JOIN' in sql_upper:
                    result_match += 0.3
                if 'COUNT' in sql_upper and 'GROUP BY' in sql_upper:
                    result_match += 0.4
            
            # Query quality for complex operations
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.3  # Uses JOIN
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.2  # Uses GROUP BY
                if 'COUNT' in result['sql'].upper():
                    quality_score += 0.2  # Uses aggregate
                if execution_time < 2.0:  # More lenient for complex queries
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success'], f"Query should succeed: {result.get('error')}"
            assert 'customers' in result['sql'].lower()
            assert 'orders' in result['sql'].lower()
            assert 'join' in result['sql'].lower()
            assert accuracy_score >= 0.8, f"Accuracy score {accuracy_score} should be >= 0.8"
    
    def test_top_products_by_revenue(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show top 5 products by total revenue."""
        question = "Show top 5 products by total revenue"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT p.productname, SUM(od.unitprice * od.quantity) as total_revenue
                                   FROM products p
                                   JOIN order_details od ON p.productid = od.productid
                                   GROUP BY p.productname
                                   ORDER BY total_revenue DESC
                                   LIMIT 5"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'PRODUCTS' in sql_upper and ('ORDER_DETAILS' in sql_upper or 'ORDERDETAILS' in sql_upper):
                    result_match += 0.3
                if 'SUM' in sql_upper:
                    result_match += 0.2
                if 'ORDER BY' in sql_upper and 'DESC' in sql_upper:
                    result_match += 0.2
                if 'LIMIT' in sql_upper and '5' in result['sql']:
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.3
                if 'SUM' in result['sql'].upper():
                    quality_score += 0.2
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 2.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_monthly_sales_analysis(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show monthly sales totals for 1996."""
        question = "Show monthly sales totals for 1996"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT EXTRACT(MONTH FROM o.orderdate) as month,
                                          SUM(od.unitprice * od.quantity) as monthly_sales
                                   FROM orders o
                                   JOIN order_details od ON o.orderid = od.orderid
                                   WHERE EXTRACT(YEAR FROM o.orderdate) = 1996
                                   GROUP BY EXTRACT(MONTH FROM o.orderdate)
                                   ORDER BY month"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'ORDERS' in sql_upper and ('ORDER_DETAILS' in sql_upper or 'ORDERDETAILS' in sql_upper):
                    result_match += 0.3
                if 'SUM' in sql_upper:
                    result_match += 0.2
                if '1996' in result['sql']:
                    result_match += 0.2
                if 'GROUP BY' in sql_upper and ('MONTH' in sql_upper or 'EXTRACT' in sql_upper):
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.2
                if 'WHERE' in result['sql'].upper():
                    quality_score += 0.2
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.2
                if 'EXTRACT' in result['sql'].upper() or 'DATE' in result['sql'].upper():
                    quality_score += 0.1
                if execution_time < 2.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'orders' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_customer_category_preferences(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show which product categories each customer prefers."""
        question = "Show which product categories each customer orders most"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT c.companyname, cat.categoryname, COUNT(*) as order_count
                                   FROM customers c
                                   JOIN orders o ON c.customerid = o.customerid
                                   JOIN order_details od ON o.orderid = od.orderid
                                   JOIN products p ON od.productid = p.productid
                                   JOIN categories cat ON p.categoryid = cat.categoryid
                                   GROUP BY c.companyname, cat.categoryname
                                   ORDER BY c.companyname, order_count DESC"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                tables_found = 0
                for table in ['CUSTOMERS', 'ORDERS', 'PRODUCTS', 'CATEGORIES']:
                    if table in sql_upper:
                        tables_found += 1
                
                if tables_found >= 3:
                    result_match += 0.4
                if 'JOIN' in sql_upper:
                    result_match += 0.3
                if 'GROUP BY' in sql_upper:
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                join_count = result['sql'].upper().count('JOIN')
                if join_count >= 3:
                    quality_score += 0.4  # Multiple JOINs
                elif join_count >= 1:
                    quality_score += 0.2
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 3.0:  # Very lenient for complex multi-table query
                    quality_score += 0.3
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'customers' in result['sql'].lower()
            assert 'join' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_employee_sales_performance(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show employee sales performance with rankings."""
        question = "Show employee sales performance ranked by total sales"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT e.firstname || ' ' || e.lastname as employee_name,
                                          SUM(od.unitprice * od.quantity) as total_sales,
                                          RANK() OVER (ORDER BY SUM(od.unitprice * od.quantity) DESC) as sales_rank
                                   FROM employees e
                                   JOIN orders o ON e.employeeid = o.employeeid
                                   JOIN order_details od ON o.orderid = od.orderid
                                   GROUP BY e.employeeid, e.firstname, e.lastname
                                   ORDER BY total_sales DESC"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'EMPLOYEES' in sql_upper:
                    result_match += 0.3
                if 'SUM' in sql_upper:
                    result_match += 0.2
                if 'RANK' in sql_upper or 'ORDER BY' in sql_upper:
                    result_match += 0.3
                if 'GROUP BY' in sql_upper:
                    result_match += 0.2
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.2
                if 'RANK' in result['sql'].upper() or 'OVER' in result['sql'].upper():
                    quality_score += 0.3  # Window functions
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 2.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'employees' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_product_profitability_analysis(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Analyze product profitability with complex calculations."""
        question = "Show products with their total revenue and average order value"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT p.productname,
                                          SUM(od.unitprice * od.quantity) as total_revenue,
                                          AVG(od.unitprice * od.quantity) as avg_order_value,
                                          COUNT(DISTINCT od.orderid) as order_count
                                   FROM products p
                                   JOIN order_details od ON p.productid = od.productid
                                   GROUP BY p.productid, p.productname
                                   HAVING SUM(od.unitprice * od.quantity) > 1000
                                   ORDER BY total_revenue DESC"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'PRODUCTS' in sql_upper:
                    result_match += 0.3
                if 'SUM' in sql_upper and 'AVG' in sql_upper:
                    result_match += 0.4
                if 'GROUP BY' in sql_upper:
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.2
                aggregate_count = 0
                for func in ['SUM', 'AVG', 'COUNT']:
                    if func in result['sql'].upper():
                        aggregate_count += 1
                if aggregate_count >= 2:
                    quality_score += 0.3  # Multiple aggregates
                if 'HAVING' in result['sql'].upper():
                    quality_score += 0.2  # Advanced filtering
                if execution_time < 2.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert accuracy_score >= 0.8


class TestComplexQueryValidation:
    """Test validation of complex queries."""
    
    def test_complex_query_security(self, query_validator):
        """Test that complex queries pass security validation."""
        complex_queries = [
            """SELECT c.companyname, COUNT(o.orderid) 
               FROM customers c 
               LEFT JOIN orders o ON c.customerid = o.customerid 
               GROUP BY c.companyname""",
            
            """SELECT p.productname, SUM(od.quantity * od.unitprice) as revenue
               FROM products p
               JOIN order_details od ON p.productid = od.productid
               GROUP BY p.productname
               ORDER BY revenue DESC""",
            
            """SELECT EXTRACT(YEAR FROM orderdate) as year, COUNT(*) as order_count
               FROM orders
               WHERE orderdate >= '1996-01-01'
               GROUP BY EXTRACT(YEAR FROM orderdate)"""
        ]
        
        for sql in complex_queries:
            is_valid, cleaned_sql, error = query_validator.validate_query(sql)
            assert is_valid, f"Complex query should be valid: {sql[:50]}..., Error: {error}"
            assert cleaned_sql is not None
    
    def test_complex_query_table_extraction(self, query_validator):
        """Test table extraction from complex queries."""
        complex_sql = """SELECT c.companyname, COUNT(o.orderid) 
                        FROM customers c 
                        LEFT JOIN orders o ON c.customerid = o.customerid 
                        GROUP BY c.companyname"""
        
        tables = query_validator.extract_table_names(complex_sql)
        expected_tables = ['customers', 'orders']
        
        for expected_table in expected_tables:
            assert expected_table in tables, f"Should extract table: {expected_table}"
