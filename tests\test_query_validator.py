"""Unit tests for QueryValidator module."""

import pytest
from src.query_validator import QueryValidator


class TestQueryValidator:
    """Test cases for QueryValidator class."""
    
    def test_init(self, query_validator):
        """Test QueryValidator initialization."""
        assert query_validator.max_query_length == 10000
        assert query_validator.max_tables_per_query == 10
        assert len(query_validator.ALLOWED_KEYWORDS) > 0
        assert len(query_validator.ALLOWED_FUNCTIONS) > 0
        assert len(query_validator.DANGEROUS_PATTERNS) > 0
    
    def test_validate_simple_select(self, query_validator):
        """Test validation of simple SELECT queries."""
        valid_queries = [
            "SELECT * FROM customers",
            "SELECT name, email FROM customers WHERE country = 'USA'",
            "SELECT COUNT(*) FROM orders",
            "SELECT c.name, o.total FROM customers c JOIN orders o ON c.id = o.customer_id"
        ]
        
        for sql in valid_queries:
            is_valid, cleaned_sql, error = query_validator.validate_query(sql)
            assert is_valid, f"Query should be valid: {sql}, Error: {error}"
            assert cleaned_sql is not None
            assert error is None
    
    def test_validate_dangerous_queries(self, query_validator):
        """Test validation blocks dangerous queries."""
        dangerous_queries = [
            "SELECT * FROM customers; DROP TABLE customers;",
            "DELETE FROM customers WHERE id = 1",
            "UPDATE customers SET name = 'hacked'",
            "INSERT INTO customers VALUES ('hack', 'hack')",
            "CREATE TABLE malicious (id INT)",
            "ALTER TABLE customers ADD COLUMN malicious TEXT",
            "TRUNCATE TABLE customers",
            "SELECT * FROM customers -- comment",
            "SELECT * FROM customers /* comment */",
            "EXEC xp_cmdshell('dir')",
            "SELECT * FROM information_schema.tables",
            "SELECT * FROM pg_catalog.pg_tables"
        ]
        
        for sql in dangerous_queries:
            is_valid, _, error = query_validator.validate_query(sql)
            assert not is_valid, f"Query should be invalid: {sql}"
            assert error is not None
    
    def test_basic_validation(self, query_validator):
        """Test basic validation checks."""
        # Empty query
        is_valid, error = query_validator._basic_validation("")
        assert not is_valid
        assert "Empty query" in error
        
        # Query too long
        long_query = "SELECT * FROM customers WHERE " + "name = 'test' AND " * 1000
        is_valid, error = query_validator._basic_validation(long_query)
        assert not is_valid
        assert "Query too long" in error
        
        # Multiple statements
        is_valid, error = query_validator._basic_validation("SELECT * FROM customers; SELECT * FROM orders;")
        assert not is_valid
        assert "Multiple statements not allowed" in error
    
    def test_check_dangerous_patterns(self, query_validator):
        """Test dangerous pattern detection."""
        test_cases = [
            ("SELECT * FROM customers; DROP TABLE users;", False),
            ("SELECT * FROM customers -- comment", False),
            ("SELECT * FROM customers /* comment */", False),
            ("SELECT * FROM information_schema.tables", False),
            ("SELECT name FROM customers", True),
            ("SELECT COUNT(*) FROM orders", True)
        ]
        
        for sql, should_be_safe in test_cases:
            is_valid, error = query_validator._check_dangerous_patterns(sql)
            if should_be_safe:
                assert is_valid, f"Query should be safe: {sql}"
                assert error is None
            else:
                assert not is_valid, f"Query should be dangerous: {sql}"
                assert error is not None
    
    def test_validate_statement_type(self, query_validator):
        """Test statement type validation."""
        import sqlparse
        
        # Valid SELECT statements
        valid_statements = [
            "SELECT * FROM customers",
            "SELECT name FROM customers WHERE id = 1"
        ]
        
        for sql in valid_statements:
            parsed = sqlparse.parse(sql)[0]
            is_valid, error = query_validator._validate_statement_type(parsed)
            assert is_valid, f"SELECT statement should be valid: {sql}"
            assert error is None
        
        # Invalid statements
        invalid_statements = [
            "INSERT INTO customers VALUES (1, 'test')",
            "UPDATE customers SET name = 'test'",
            "DELETE FROM customers",
            "DROP TABLE customers"
        ]
        
        for sql in invalid_statements:
            parsed = sqlparse.parse(sql)[0]
            is_valid, error = query_validator._validate_statement_type(parsed)
            assert not is_valid, f"Non-SELECT statement should be invalid: {sql}"
            assert error is not None
    
    def test_clean_query(self, query_validator):
        """Test query cleaning."""
        test_cases = [
            ("SELECT * FROM customers -- comment", "SELECT * FROM customers"),
            ("SELECT * FROM customers /* comment */", "SELECT * FROM customers"),
            ("  SELECT   *   FROM   customers  ", "SELECT * FROM customers"),
            ("SELECT * FROM customers;", "SELECT * FROM customers"),
            ("SELECT\n*\nFROM\ncustomers", "SELECT * FROM customers")
        ]
        
        for input_sql, expected in test_cases:
            result = query_validator._clean_query(input_sql)
            assert result == expected
    
    def test_add_safety_limits(self, query_validator):
        """Test adding safety limits to queries."""
        # Query without LIMIT
        sql = "SELECT * FROM customers"
        result = query_validator._add_safety_limits(sql)
        assert "LIMIT" in result.upper()
        assert str(query_validator.max_query_length) not in result  # Should use Config.MAX_ROWS
        
        # Query with existing LIMIT
        sql_with_limit = "SELECT * FROM customers LIMIT 50"
        result = query_validator._add_safety_limits(sql_with_limit)
        assert result == sql_with_limit  # Should not modify
    
    def test_extract_table_names(self, query_validator):
        """Test table name extraction."""
        test_cases = [
            ("SELECT * FROM customers", ["customers"]),
            ("SELECT * FROM customers c JOIN orders o ON c.id = o.customer_id", ["customers", "orders"]),
            ("SELECT * FROM products p LEFT JOIN categories c ON p.category_id = c.id", ["products", "categories"]),
            ("SELECT name FROM users", ["users"])
        ]
        
        for sql, expected_tables in test_cases:
            tables = query_validator.extract_table_names(sql)
            # Check that all expected tables are found (order may vary)
            for table in expected_tables:
                assert table in tables, f"Table {table} not found in {tables} for query: {sql}"
    
    def test_validate_table_access(self, query_validator):
        """Test table access validation."""
        allowed_tables = ['customers', 'orders', 'products']
        
        # Valid table access
        valid_queries = [
            "SELECT * FROM customers",
            "SELECT * FROM customers c JOIN orders o ON c.id = o.customer_id"
        ]
        
        for sql in valid_queries:
            is_valid, error = query_validator.validate_table_access(sql, allowed_tables)
            assert is_valid, f"Query should have valid table access: {sql}, Error: {error}"
            assert error is None
        
        # Invalid table access
        invalid_queries = [
            "SELECT * FROM users",  # users not in allowed_tables
            "SELECT * FROM customers c JOIN forbidden_table f ON c.id = f.customer_id"
        ]
        
        for sql in invalid_queries:
            is_valid, error = query_validator.validate_table_access(sql, allowed_tables)
            assert not is_valid, f"Query should have invalid table access: {sql}"
            assert error is not None
    
    def test_validate_table_access_too_many_tables(self, query_validator):
        """Test validation with too many tables."""
        # Create query with more tables than allowed
        query_validator.max_tables_per_query = 2
        allowed_tables = ['t1', 't2', 't3', 't4', 't5']
        
        sql = "SELECT * FROM t1 JOIN t2 ON t1.id = t2.id JOIN t3 ON t2.id = t3.id"
        is_valid, error = query_validator.validate_table_access(sql, allowed_tables)
        assert not is_valid
        assert "Too many tables" in error
    
    def test_is_safe_query(self, query_validator):
        """Test overall query safety check."""
        allowed_tables = ['customers', 'orders', 'products']
        
        # Safe queries
        safe_queries = [
            "SELECT * FROM customers",
            "SELECT COUNT(*) FROM orders",
            "SELECT p.name FROM products p WHERE p.price > 10"
        ]
        
        for sql in safe_queries:
            assert query_validator.is_safe_query(sql, allowed_tables), f"Query should be safe: {sql}"
        
        # Unsafe queries
        unsafe_queries = [
            "DELETE FROM customers",
            "SELECT * FROM forbidden_table",
            "SELECT * FROM customers; DROP TABLE orders;"
        ]
        
        for sql in unsafe_queries:
            assert not query_validator.is_safe_query(sql, allowed_tables), f"Query should be unsafe: {sql}"
    
    def test_validate_keywords_and_functions(self, query_validator):
        """Test keyword and function validation."""
        import sqlparse
        
        # Valid keywords and functions
        valid_sql = "SELECT COUNT(*), AVG(price), UPPER(name) FROM products WHERE price > 10 GROUP BY category ORDER BY COUNT(*) DESC"
        parsed = sqlparse.parse(valid_sql)[0]
        is_valid, error = query_validator._validate_keywords_and_functions(parsed)
        assert is_valid
        assert error is None
        
        # Test with mock invalid keyword (we can't easily test this without modifying the allowed lists)
        # This test ensures the method works correctly with the current allowed lists
        common_sql = "SELECT name, email FROM customers WHERE country = 'USA' ORDER BY name"
        parsed = sqlparse.parse(common_sql)[0]
        is_valid, error = query_validator._validate_keywords_and_functions(parsed)
        assert is_valid
        assert error is None
    
    def test_edge_cases(self, query_validator):
        """Test edge cases and error handling."""
        # None input
        is_valid, _, error = query_validator.validate_query(None)
        assert not is_valid
        assert error is not None
        
        # Whitespace only
        is_valid, _, error = query_validator.validate_query("   ")
        assert not is_valid
        assert error is not None
        
        # Invalid SQL syntax
        is_valid, _, error = query_validator.validate_query("SELECT FROM WHERE")
        # Should still process but may fail at execution
        # The validator focuses on security, not syntax correctness
    
    def test_case_insensitive_validation(self, query_validator):
        """Test that validation is case insensitive."""
        queries = [
            "select * from customers",
            "SELECT * FROM CUSTOMERS",
            "Select * From Customers"
        ]
        
        for sql in queries:
            is_valid, cleaned_sql, error = query_validator.validate_query(sql)
            assert is_valid, f"Query should be valid regardless of case: {sql}"
            assert error is None
