"""Intermediate query accuracy tests for Text2SQL engine."""

import pytest
import pandas as pd
from unittest.mock import patch, <PERSON>ck
import sqlparse
import time

from src.text2sql_engine import Text2SQLEngine


class TestIntermediateQueries:
    """Test intermediate SELECT queries with WHERE, GROUP BY, ORDER BY."""
    
    @pytest.fixture
    def accuracy_metrics(self):
        """Fixture for accuracy metrics calculation."""
        def calculate_accuracy(execution_success, result_match, query_quality):
            return 0.2 * execution_success + 0.4 * result_match + 0.4 * query_quality
        return calculate_accuracy
    
    def test_customers_from_germany(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show customers from Germany."""
        question = "Show customers from Germany"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM customers WHERE country = 'Germany'"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            # Metrics calculation
            execution_success = 1.0 if result['success'] else 0.0
            
            # Check for customers table and WHERE clause
            result_match = 0.0
            if result['sql']:
                sql_lower = result['sql'].lower()
                if 'customers' in sql_lower:
                    result_match += 0.5
                if 'where' in sql_lower and 'germany' in sql_lower:
                    result_match += 0.5
            
            # Query quality
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.2
                if 'WHERE' in result['sql'].upper():
                    quality_score += 0.3  # Uses WHERE clause
                if 'SELECT' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.3
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success'], f"Query should succeed: {result.get('error')}"
            assert 'customers' in result['sql'].lower()
            assert 'where' in result['sql'].lower()
            assert accuracy_score >= 0.8, f"Accuracy score {accuracy_score} should be >= 0.8"
    
    def test_count_customers(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Count total customers."""
        question = "Count total customers"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT COUNT(*) FROM customers"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            # Check for COUNT function and customers table
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'CUSTOMERS' in sql_upper:
                    result_match += 0.5
                if 'COUNT' in sql_upper:
                    result_match += 0.5
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.2
                if 'COUNT' in result['sql'].upper():
                    quality_score += 0.4  # Uses aggregate function
                if 'SELECT' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'count' in result['sql'].lower()
            assert 'customers' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_expensive_products(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show products with price greater than 15."""
        question = "Show products with price greater than 15"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM products WHERE unitprice > 15"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_lower = result['sql'].lower()
                if 'products' in sql_lower:
                    result_match += 0.4
                if 'where' in sql_lower:
                    result_match += 0.3
                if ('price' in sql_lower or 'unitprice' in sql_lower) and ('>' in result['sql'] or '15' in result['sql']):
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.2
                if 'WHERE' in result['sql'].upper():
                    quality_score += 0.3
                if '>' in result['sql'] or 'GREATER' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.3
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert 'where' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_orders_by_date(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show orders from 1996."""
        question = "Show orders from 1996"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM orders WHERE EXTRACT(YEAR FROM orderdate) = 1996"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_lower = result['sql'].lower()
                if 'orders' in sql_lower:
                    result_match += 0.4
                if 'where' in sql_lower:
                    result_match += 0.3
                if '1996' in result['sql']:
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.2
                if 'WHERE' in result['sql'].upper():
                    quality_score += 0.3
                if 'orderdate' in result['sql'].lower() or 'date' in result['sql'].lower():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.3
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'orders' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_products_by_category(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Count products by category."""
        question = "Count products by category"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT categoryid, COUNT(*) FROM products GROUP BY categoryid"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'PRODUCTS' in sql_upper:
                    result_match += 0.3
                if 'COUNT' in sql_upper:
                    result_match += 0.3
                if 'GROUP BY' in sql_upper:
                    result_match += 0.4
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.2
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.4  # Uses GROUP BY
                if 'COUNT' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert 'count' in result['sql'].lower()
            assert 'group by' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_top_customers_by_orders(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show top 10 customers by number of orders."""
        question = "Show top 10 customers by number of orders"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT c.companyname, COUNT(o.orderid) as order_count 
                                   FROM customers c 
                                   LEFT JOIN orders o ON c.customerid = o.customerid 
                                   GROUP BY c.companyname 
                                   ORDER BY order_count DESC 
                                   LIMIT 10"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'CUSTOMERS' in sql_upper and 'ORDERS' in sql_upper:
                    result_match += 0.3
                if 'COUNT' in sql_upper:
                    result_match += 0.2
                if 'ORDER BY' in sql_upper:
                    result_match += 0.2
                if 'LIMIT' in sql_upper and '10' in result['sql']:
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.1
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.3  # Uses JOIN
                if 'GROUP BY' in result['sql'].upper():
                    quality_score += 0.2
                if 'ORDER BY' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'customers' in result['sql'].lower()
            assert 'orders' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_average_product_price(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Calculate average product price."""
        question = "What is the average product price"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT AVG(unitprice) FROM products"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'PRODUCTS' in sql_upper:
                    result_match += 0.5
                if 'AVG' in sql_upper:
                    result_match += 0.5
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.3
                if 'AVG' in result['sql'].upper():
                    quality_score += 0.4  # Uses aggregate function
                if execution_time < 1.0:
                    quality_score += 0.3
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert 'avg' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_products_ordered_by_price(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show products ordered by price descending."""
        question = "Show products ordered by price from highest to lowest"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM products ORDER BY unitprice DESC"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'PRODUCTS' in sql_upper:
                    result_match += 0.4
                if 'ORDER BY' in sql_upper:
                    result_match += 0.3
                if 'DESC' in sql_upper:
                    result_match += 0.3
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.3
                if 'ORDER BY' in result['sql'].upper():
                    quality_score += 0.4  # Uses ORDER BY
                if execution_time < 1.0:
                    quality_score += 0.3
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert 'order by' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_customers_with_orders(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show customers who have placed orders."""
        question = "Show customers who have placed orders"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = """SELECT DISTINCT c.companyname 
                                   FROM customers c 
                                   INNER JOIN orders o ON c.customerid = o.customerid"""
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            
            result_match = 0.0
            if result['sql']:
                sql_upper = result['sql'].upper()
                if 'CUSTOMERS' in sql_upper and 'ORDERS' in sql_upper:
                    result_match += 0.5
                if 'JOIN' in sql_upper:
                    result_match += 0.5
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.2
                if 'JOIN' in result['sql'].upper():
                    quality_score += 0.4  # Uses JOIN
                if 'DISTINCT' in result['sql'].upper():
                    quality_score += 0.2
                if execution_time < 1.0:
                    quality_score += 0.2
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'customers' in result['sql'].lower()
            assert 'orders' in result['sql'].lower()
            assert accuracy_score >= 0.8
