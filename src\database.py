"""Database layer with SQLAlchemy integration for Text2SQL Analytics System."""

import pandas as pd
import logging
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
import time
from sqlalchemy import create_engine, text, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import S<PERSON><PERSON>lchemyError, OperationalError
from sqlalchemy.pool import QueuePool

from .config import Config

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections, queries, and transactions."""
    
    def __init__(self, use_query_user: bool = False):
        """Initialize DatabaseManager with connection pooling."""
        self.use_query_user = use_query_user
        self.admin_engine: Optional[Engine] = None
        self.query_engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        
        # Initialize connections
        self._setup_connections()
    
    def _setup_connections(self) -> None:
        """Setup database connections with proper configuration."""
        try:
            # Admin connection for setup and data loading
            self.admin_engine = create_engine(
                Config.DB_URL,
                poolclass=QueuePool,
                pool_size=Config.DB_POOL_SIZE,
                max_overflow=Config.DB_MAX_OVERFLOW,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections every hour
                echo=Config.ENVIRONMENT == "development"
            )
            
            # Query connection for read-only operations
            self.query_engine = create_engine(
                Config.get_query_url(),
                poolclass=QueuePool,
                pool_size=Config.DB_POOL_SIZE,
                max_overflow=Config.DB_MAX_OVERFLOW,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=Config.ENVIRONMENT == "development"
            )
            
            # Session factory for admin operations
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.admin_engine
            )
            
            logger.info("Database connections initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database connections: {str(e)}")
            raise
    
    def get_connection(self, use_query_user: bool = None) -> Engine:
        """Get database connection engine."""
        if use_query_user is None:
            use_query_user = self.use_query_user
            
        if use_query_user:
            if not self.query_engine:
                raise RuntimeError("Query engine not initialized")
            return self.query_engine
        else:
            if not self.admin_engine:
                raise RuntimeError("Admin engine not initialized")
            return self.admin_engine
    
    @contextmanager
    def get_session(self):
        """Get database session with automatic cleanup."""
        if not self.SessionLocal:
            raise RuntimeError("Session factory not initialized")
            
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {str(e)}")
            raise
        finally:
            session.close()
    
    def execute_query(self, sql: str, params: Optional[Dict[str, Any]] = None, 
                     use_query_user: bool = True, timeout: Optional[int] = None) -> pd.DataFrame:
        """Execute SQL query and return results as DataFrame."""
        if timeout is None:
            timeout = Config.QUERY_TIMEOUT
            
        try:
            engine = self.get_connection(use_query_user=use_query_user)
            
            # Add timeout and row limit to query if not present
            modified_sql = self._add_query_limits(sql)
            
            start_time = time.time()
            
            with engine.connect() as conn:
                # Set statement timeout
                conn.execute(text(f"SET statement_timeout = '{timeout}s'"))
                
                # Execute query
                if params:
                    result = conn.execute(text(modified_sql), params)
                else:
                    result = conn.execute(text(modified_sql))
                
                # Convert to DataFrame
                df = pd.DataFrame(result.fetchall(), columns=result.keys())
                
                execution_time = time.time() - start_time
                logger.info(f"Query executed in {execution_time:.3f}s, returned {len(df)} rows")
                
                return df
                
        except OperationalError as e:
            if "timeout" in str(e).lower():
                logger.error(f"Query timeout after {timeout}s: {sql[:100]}...")
                raise TimeoutError(f"Query execution timeout ({timeout}s)")
            else:
                logger.error(f"Database operational error: {str(e)}")
                raise
        except SQLAlchemyError as e:
            logger.error(f"Database error executing query: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error executing query: {str(e)}")
            raise
    
    def _add_query_limits(self, sql: str) -> str:
        """Add row limit to query if not present."""
        sql_upper = sql.upper().strip()
        
        # Check if LIMIT is already present
        if "LIMIT" not in sql_upper:
            # Add LIMIT clause
            if sql.rstrip().endswith(';'):
                sql = sql.rstrip()[:-1]  # Remove trailing semicolon
            sql += f" LIMIT {Config.MAX_ROWS}"
        
        return sql
    
    def execute_script(self, script: str, use_query_user: bool = False) -> None:
        """Execute SQL script (multiple statements)."""
        try:
            engine = self.get_connection(use_query_user=use_query_user)
            
            with engine.connect() as conn:
                # Split script into individual statements
                statements = [stmt.strip() for stmt in script.split(';') if stmt.strip()]
                
                for statement in statements:
                    if statement:
                        conn.execute(text(statement))
                        conn.commit()
                
                logger.info(f"Executed script with {len(statements)} statements")
                
        except SQLAlchemyError as e:
            logger.error(f"Error executing script: {str(e)}")
            raise
    
    def insert_dataframe(self, df: pd.DataFrame, table_name: str, 
                        if_exists: str = 'append', batch_size: int = 1000) -> None:
        """Insert DataFrame into database table in batches."""
        try:
            engine = self.get_connection(use_query_user=False)  # Use admin connection
            
            # Insert in batches for better performance
            total_rows = len(df)
            logger.info(f"Inserting {total_rows} rows into {table_name} in batches of {batch_size}")
            
            for i in range(0, total_rows, batch_size):
                batch_df = df.iloc[i:i + batch_size]
                batch_df.to_sql(
                    table_name,
                    engine,
                    if_exists=if_exists if i == 0 else 'append',
                    index=False,
                    method='multi'
                )
                logger.info(f"Inserted batch {i//batch_size + 1}/{(total_rows-1)//batch_size + 1}")
            
            logger.info(f"Successfully inserted {total_rows} rows into {table_name}")
            
        except SQLAlchemyError as e:
            logger.error(f"Error inserting data into {table_name}: {str(e)}")
            raise
    
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists in database."""
        try:
            engine = self.get_connection(use_query_user=False)
            
            with engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = :table_name
                    )
                """), {"table_name": table_name})
                
                return result.scalar()
                
        except SQLAlchemyError as e:
            logger.error(f"Error checking table existence: {str(e)}")
            return False
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get information about a table's structure."""
        try:
            engine = self.get_connection(use_query_user=True)
            
            with engine.connect() as conn:
                # Get column information
                columns_result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = 'public' AND table_name = :table_name
                    ORDER BY ordinal_position
                """), {"table_name": table_name})
                
                columns = [dict(row._mapping) for row in columns_result]
                
                # Get primary key information
                pk_result = conn.execute(text("""
                    SELECT a.attname
                    FROM pg_index i
                    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
                    WHERE i.indrelid = :table_name::regclass AND i.indisprimary
                """), {"table_name": table_name})
                
                primary_keys = [row[0] for row in pk_result]
                
                return {
                    "columns": columns,
                    "primary_keys": primary_keys
                }
                
        except SQLAlchemyError as e:
            logger.error(f"Error getting table info for {table_name}: {str(e)}")
            return {"columns": [], "primary_keys": []}
    
    def close_connections(self) -> None:
        """Close all database connections."""
        try:
            if self.admin_engine:
                self.admin_engine.dispose()
            if self.query_engine:
                self.query_engine.dispose()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database connections: {str(e)}")
    
    def __del__(self):
        """Cleanup connections on object destruction."""
        self.close_connections()
