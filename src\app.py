"""FastAPI application for Text2SQL Analytics System."""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging
import time
from contextlib import asynccontextmanager

from .config import Config
from .database import DatabaseManager
from .text2sql_engine import Text2SQLEngine
from .query_validator import QueryValidator
from .utils import QueryMetrics, JSONEncoder

# Setup logging
Config.setup_logging()
logger = logging.getLogger(__name__)

# Global instances
db_manager: Optional[DatabaseManager] = None
text2sql_engine: Optional[Text2SQLEngine] = None
query_validator: Optional[QueryValidator] = None
query_metrics: Optional[QueryMetrics] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global db_manager, text2sql_engine, query_validator, query_metrics
    
    # Startup
    logger.info("Starting Text2SQL Analytics API...")
    
    try:
        # Validate configuration
        if not Config.validate_config():
            raise RuntimeError("Configuration validation failed")
        
        # Initialize components
        db_manager = DatabaseManager(use_query_user=True)
        text2sql_engine = Text2SQLEngine(db_manager)
        query_validator = QueryValidator()
        query_metrics = QueryMetrics()
        
        logger.info("Text2SQL Analytics API started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start API: {str(e)}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Text2SQL Analytics API...")
    if db_manager:
        db_manager.close_connections()


# Create FastAPI app
app = FastAPI(
    title="Text2SQL Analytics API",
    description="Production-ready Text-to-SQL conversion API with PostgreSQL and Google Gemini",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models
class QueryRequest(BaseModel):
    """Request model for natural language queries."""
    question: str = Field(..., min_length=1, max_length=1000, description="Natural language question")
    explain: bool = Field(False, description="Include query execution plan")
    
    class Config:
        schema_extra = {
            "example": {
                "question": "Show me the top 5 customers by total order value",
                "explain": False
            }
        }


class QueryResponse(BaseModel):
    """Response model for query results."""
    question: str
    sql: Optional[str]
    results: List[Dict[str, Any]]
    row_count: int
    execution_time: float
    success: bool
    error: Optional[str]
    execution_plan: Optional[Dict[str, Any]] = None
    
    class Config:
        json_encoders = {
            **JSONEncoder().default.__dict__
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: str
    database_connected: bool
    api_configured: bool
    cache_info: Optional[Dict[str, Any]] = None


class SchemaResponse(BaseModel):
    """Schema information response model."""
    tables: List[str]
    table_details: Dict[str, Any]
    total_tables: int


class MetricsResponse(BaseModel):
    """Query metrics response model."""
    total_queries: int
    successful_queries: int
    success_rate: float
    average_execution_time: float
    average_row_count: float
    common_errors: List[Dict[str, Any]]


# Dependency functions
def get_text2sql_engine() -> Text2SQLEngine:
    """Get Text2SQL engine instance."""
    if text2sql_engine is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Text2SQL engine not initialized"
        )
    return text2sql_engine


def get_query_metrics() -> QueryMetrics:
    """Get query metrics instance."""
    if query_metrics is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Query metrics not initialized"
        )
    return query_metrics


# API endpoints
@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Text2SQL Analytics API",
        "version": "1.0.0",
        "description": "Convert natural language questions to SQL queries",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        db_connected = False
        if db_manager:
            try:
                test_df = db_manager.execute_query("SELECT 1 as test")
                db_connected = len(test_df) > 0
            except Exception:
                db_connected = False
        
        # Check API configuration
        api_configured = bool(Config.GEMINI_API_KEY)
        
        # Get cache info
        cache_info = None
        if text2sql_engine:
            cache_info = text2sql_engine.get_cache_info()
        
        return HealthResponse(
            status="healthy" if db_connected and api_configured else "unhealthy",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            database_connected=db_connected,
            api_configured=api_configured,
            cache_info=cache_info
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Health check failed: {str(e)}"
        )


@app.post("/query", response_model=QueryResponse)
async def execute_query(
    request: QueryRequest,
    engine: Text2SQLEngine = Depends(get_text2sql_engine),
    metrics: QueryMetrics = Depends(get_query_metrics)
):
    """Execute natural language query and return results."""
    start_time = time.time()
    
    try:
        logger.info(f"Processing query: {request.question[:100]}...")
        
        # Execute the question
        result = engine.execute_question(request.question)
        
        execution_time = time.time() - start_time
        
        # Get execution plan if requested
        execution_plan = None
        if request.explain and result['success'] and result['sql']:
            try:
                plan_result = engine.explain_query(result['sql'])
                if plan_result['success']:
                    execution_plan = plan_result['execution_plan']
            except Exception as e:
                logger.warning(f"Failed to get execution plan: {str(e)}")
        
        # Record metrics
        metrics.record_query(
            question=request.question,
            sql=result['sql'],
            execution_time=execution_time,
            row_count=result['row_count'],
            success=result['success'],
            error=result['error']
        )
        
        response = QueryResponse(
            question=request.question,
            sql=result['sql'],
            results=result['results'],
            row_count=result['row_count'],
            execution_time=execution_time,
            success=result['success'],
            error=result['error'],
            execution_plan=execution_plan
        )
        
        if result['success']:
            logger.info(f"Query executed successfully in {execution_time:.3f}s, returned {result['row_count']} rows")
        else:
            logger.warning(f"Query failed: {result['error']}")
        
        return response
        
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = str(e)
        
        logger.error(f"Query execution failed: {error_msg}")
        
        # Record failed query metrics
        metrics.record_query(
            question=request.question,
            sql=None,
            execution_time=execution_time,
            row_count=0,
            success=False,
            error=error_msg
        )
        
        return QueryResponse(
            question=request.question,
            sql=None,
            results=[],
            row_count=0,
            execution_time=execution_time,
            success=False,
            error=error_msg
        )


@app.get("/schema", response_model=SchemaResponse)
async def get_schema(engine: Text2SQLEngine = Depends(get_text2sql_engine)):
    """Get database schema information."""
    try:
        schema_info = engine.get_schema_info()
        
        return SchemaResponse(
            tables=schema_info['tables'],
            table_details=schema_info['table_details'],
            total_tables=len(schema_info['tables'])
        )
        
    except Exception as e:
        logger.error(f"Failed to get schema info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get schema information: {str(e)}"
        )


@app.get("/metrics", response_model=MetricsResponse)
async def get_metrics(metrics: QueryMetrics = Depends(get_query_metrics)):
    """Get query execution metrics."""
    try:
        metrics_summary = metrics.get_metrics_summary()
        
        return MetricsResponse(**metrics_summary)
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics: {str(e)}"
        )


@app.post("/cache/clear")
async def clear_cache(engine: Text2SQLEngine = Depends(get_text2sql_engine)):
    """Clear the SQL generation cache."""
    try:
        engine.clear_cache()
        return {"message": "Cache cleared successfully"}
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"error": "Internal server error", "status_code": 500}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
