"""Tests for database manager functionality."""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
import sqlalchemy
from sqlalchemy import text

from src.database import DatabaseManager
from src.config import Config


class TestDatabaseManager:
    """Test DatabaseManager class functionality."""
    
    @pytest.fixture
    def mock_engine(self):
        """Mock SQLAlchemy engine."""
        engine = Mock()
        connection = Mock()
        result = Mock()
        
        # Setup mock chain
        engine.connect.return_value.__enter__.return_value = connection
        connection.execute.return_value = result
        result.fetchall.return_value = [('test_row',)]
        result.rowcount = 1
        
        return engine
    
    @pytest.fixture
    def db_manager(self, mock_engine):
        """DatabaseManager instance with mocked engine."""
        with patch('src.database.create_engine', return_value=mock_engine):
            manager = DatabaseManager()
            manager.engine = mock_engine
            return manager
    
    def test_init_with_default_config(self):
        """Test DatabaseManager initialization with default config."""
        with patch('src.database.create_engine') as mock_create_engine:
            manager = DatabaseManager()
            
            # Should use admin URL by default
            mock_create_engine.assert_called_once()
            call_args = mock_create_engine.call_args[0]
            assert Config.DB_URL in str(call_args[0])
    
    def test_init_with_query_user(self):
        """Test DatabaseManager initialization with query user."""
        with patch('src.database.create_engine') as mock_create_engine:
            manager = DatabaseManager(use_query_user=True)
            
            mock_create_engine.assert_called_once()
            call_args = mock_create_engine.call_args[0]
            # Should use query user URL
            assert 'admin:1234' in str(call_args[0])
    
    def test_execute_query_success(self, db_manager, mock_engine):
        """Test successful query execution."""
        # Setup mock
        connection = mock_engine.connect.return_value.__enter__.return_value
        result = Mock()
        result.fetchall.return_value = [{'id': 1, 'name': 'test'}]
        result.rowcount = 1
        connection.execute.return_value = result
        
        # Execute query
        sql = "SELECT * FROM customers LIMIT 5"
        results, row_count = db_manager.execute_query(sql)
        
        # Verify results
        assert results == [{'id': 1, 'name': 'test'}]
        assert row_count == 1
        connection.execute.assert_called_once()
    
    def test_execute_query_with_timeout(self, db_manager, mock_engine):
        """Test query execution with timeout."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        result = Mock()
        result.fetchall.return_value = []
        result.rowcount = 0
        connection.execute.return_value = result
        
        sql = "SELECT * FROM products"
        results, row_count = db_manager.execute_query(sql, timeout=10)
        
        assert results == []
        assert row_count == 0
    
    def test_execute_query_with_limit_injection(self, db_manager, mock_engine):
        """Test automatic LIMIT injection for queries without LIMIT."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        result = Mock()
        result.fetchall.return_value = []
        result.rowcount = 0
        connection.execute.return_value = result
        
        sql = "SELECT * FROM customers"
        db_manager.execute_query(sql)
        
        # Should inject LIMIT
        executed_sql = connection.execute.call_args[0][0]
        assert 'LIMIT' in str(executed_sql).upper()
    
    def test_execute_query_preserves_existing_limit(self, db_manager, mock_engine):
        """Test that existing LIMIT clauses are preserved."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        result = Mock()
        result.fetchall.return_value = []
        result.rowcount = 0
        connection.execute.return_value = result
        
        sql = "SELECT * FROM customers LIMIT 50"
        db_manager.execute_query(sql)
        
        # Should not modify existing LIMIT
        executed_sql = str(connection.execute.call_args[0][0])
        assert executed_sql.count('LIMIT') == 1
        assert 'LIMIT 50' in executed_sql
    
    def test_execute_query_database_error(self, db_manager, mock_engine):
        """Test query execution with database error."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        connection.execute.side_effect = sqlalchemy.exc.DatabaseError("Connection failed", None, None)
        
        with pytest.raises(Exception) as exc_info:
            db_manager.execute_query("SELECT * FROM customers")
        
        assert "Connection failed" in str(exc_info.value)
    
    def test_execute_script_success(self, db_manager, mock_engine):
        """Test successful script execution."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        
        script = """
        CREATE TABLE test_table (id INTEGER);
        INSERT INTO test_table VALUES (1);
        """
        
        db_manager.execute_script(script)
        
        # Should execute each statement
        assert connection.execute.call_count >= 2
    
    def test_execute_script_with_transaction(self, db_manager, mock_engine):
        """Test script execution with transaction management."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        transaction = Mock()
        connection.begin.return_value.__enter__.return_value = transaction
        
        script = "CREATE TABLE test (id INTEGER);"
        db_manager.execute_script(script)
        
        connection.begin.assert_called_once()
    
    def test_insert_dataframe_success(self, db_manager):
        """Test successful DataFrame insertion."""
        # Create test DataFrame
        df = pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['Alice', 'Bob', 'Charlie'],
            'age': [25, 30, 35]
        })
        
        with patch.object(df, 'to_sql') as mock_to_sql:
            db_manager.insert_dataframe(df, 'test_table')
            
            mock_to_sql.assert_called_once_with(
                'test_table',
                db_manager.engine,
                if_exists='append',
                index=False,
                method='multi',
                chunksize=500
            )
    
    def test_insert_dataframe_replace_mode(self, db_manager):
        """Test DataFrame insertion with replace mode."""
        df = pd.DataFrame({'id': [1], 'name': ['test']})
        
        with patch.object(df, 'to_sql') as mock_to_sql:
            db_manager.insert_dataframe(df, 'test_table', if_exists='replace')
            
            mock_to_sql.assert_called_once()
            call_kwargs = mock_to_sql.call_args[1]
            assert call_kwargs['if_exists'] == 'replace'
    
    def test_table_exists_true(self, db_manager, mock_engine):
        """Test table_exists returns True for existing table."""
        # Mock inspector
        with patch('src.database.inspect') as mock_inspect:
            inspector = Mock()
            inspector.has_table.return_value = True
            mock_inspect.return_value = inspector
            
            result = db_manager.table_exists('customers')
            
            assert result is True
            inspector.has_table.assert_called_once_with('customers')
    
    def test_table_exists_false(self, db_manager, mock_engine):
        """Test table_exists returns False for non-existing table."""
        with patch('src.database.inspect') as mock_inspect:
            inspector = Mock()
            inspector.has_table.return_value = False
            mock_inspect.return_value = inspector
            
            result = db_manager.table_exists('nonexistent_table')
            
            assert result is False
    
    def test_get_table_info_success(self, db_manager, mock_engine):
        """Test successful table info retrieval."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        result = Mock()
        result.fetchall.return_value = [
            ('id', 'INTEGER', 'NO', 'PRI', None, ''),
            ('name', 'VARCHAR(255)', 'YES', '', None, '')
        ]
        connection.execute.return_value = result
        
        table_info = db_manager.get_table_info('customers')
        
        assert len(table_info) == 2
        assert table_info[0][0] == 'id'
        assert table_info[1][0] == 'name'
    
    def test_get_table_info_nonexistent_table(self, db_manager, mock_engine):
        """Test table info for non-existent table."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        connection.execute.side_effect = sqlalchemy.exc.DatabaseError("Table doesn't exist", None, None)
        
        with pytest.raises(Exception):
            db_manager.get_table_info('nonexistent_table')
    
    def test_close_connection(self, db_manager, mock_engine):
        """Test connection cleanup."""
        db_manager.close()
        
        # Should dispose of engine
        mock_engine.dispose.assert_called_once()
    
    def test_context_manager(self, mock_engine):
        """Test DatabaseManager as context manager."""
        with patch('src.database.create_engine', return_value=mock_engine):
            with DatabaseManager() as db_manager:
                assert db_manager.engine == mock_engine
            
            # Should dispose after context
            mock_engine.dispose.assert_called_once()
    
    def test_connection_pool_configuration(self):
        """Test that connection pool is configured correctly."""
        with patch('src.database.create_engine') as mock_create_engine:
            DatabaseManager()
            
            # Check pool configuration
            call_kwargs = mock_create_engine.call_args[1]
            assert call_kwargs['pool_size'] == 5
            assert call_kwargs['max_overflow'] == 10
            assert call_kwargs['pool_timeout'] == 30
            assert call_kwargs['pool_recycle'] == 3600
    
    def test_query_with_parameters(self, db_manager, mock_engine):
        """Test parameterized query execution."""
        connection = mock_engine.connect.return_value.__enter__.return_value
        result = Mock()
        result.fetchall.return_value = [{'id': 1, 'name': 'Alice'}]
        result.rowcount = 1
        connection.execute.return_value = result
        
        sql = "SELECT * FROM customers WHERE country = :country"
        params = {'country': 'Germany'}
        
        results, row_count = db_manager.execute_query(sql, params=params)
        
        assert results == [{'id': 1, 'name': 'Alice'}]
        assert row_count == 1
        
        # Verify parameterized execution
        executed_query = connection.execute.call_args[0][0]
        assert ':country' in str(executed_query) or 'Germany' in str(executed_query)
    
    def test_batch_insert_large_dataframe(self, db_manager):
        """Test batch insertion for large DataFrames."""
        # Create large DataFrame
        large_df = pd.DataFrame({
            'id': range(2000),
            'value': [f'value_{i}' for i in range(2000)]
        })
        
        with patch.object(large_df, 'to_sql') as mock_to_sql:
            db_manager.insert_dataframe(large_df, 'large_table')
            
            # Should use chunking
            call_kwargs = mock_to_sql.call_args[1]
            assert call_kwargs['chunksize'] == 500
