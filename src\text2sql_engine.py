"""Text2SQL engine with Google Gemini API integration."""

import logging
from typing import Dict, List, Optional, Any, Tuple
from functools import lru_cache
import hashlib
import json
import google.generativeai as genai

from .config import Config
from .database import DatabaseManager
from .query_validator import QueryValidator

logger = logging.getLogger(__name__)


class Text2SQLEngine:
    """Converts natural language questions to SQL queries using Google Gemini."""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """Initialize Text2SQL engine."""
        self.db_manager = db_manager or DatabaseManager(use_query_user=True)
        self.validator = QueryValidator()
        self.schema_context = ""
        self.table_info = {}
        
        # Configure Gemini API
        if not Config.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY not found in configuration")
        
        genai.configure(api_key=Config.GEMINI_API_KEY)
        self.model = genai.GenerativeModel('gemini-pro')
        
        # Load schema context
        self._load_schema_context()
        
        logger.info("Text2SQL engine initialized successfully")
    
    def _load_schema_context(self) -> None:
        """Load database schema context for prompt engineering."""
        try:
            # Get list of tables
            tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """
            
            tables_df = self.db_manager.execute_query(tables_query)
            table_names = tables_df['table_name'].tolist()
            
            schema_parts = []
            schema_parts.append("=== DATABASE SCHEMA ===")
            schema_parts.append("This is a Northwind database with the following tables:")
            schema_parts.append("")
            
            # Get detailed info for each table
            for table_name in table_names:
                table_info = self.db_manager.get_table_info(table_name)
                self.table_info[table_name] = table_info
                
                schema_parts.append(f"Table: {table_name}")
                schema_parts.append("Columns:")
                
                for col in table_info['columns']:
                    nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                    default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                    schema_parts.append(f"  - {col['column_name']}: {col['data_type']} {nullable}{default}")
                
                if table_info['primary_keys']:
                    schema_parts.append(f"Primary Key: {', '.join(table_info['primary_keys'])}")
                
                schema_parts.append("")
            
            # Add relationship information
            schema_parts.append("=== TABLE RELATIONSHIPS ===")
            schema_parts.extend([
                "- customers.customerid -> orders.customerid",
                "- employees.employeeid -> orders.employeeid", 
                "- shippers.shipperid -> orders.shipvia",
                "- orders.orderid -> order_details.orderid",
                "- products.productid -> order_details.productid",
                "- suppliers.supplierid -> products.supplierid",
                "- categories.categoryid -> products.categoryid",
                "- employees.reportsto -> employees.employeeid (self-reference)",
                ""
            ])
            
            # Add sample queries
            schema_parts.append("=== SAMPLE QUERIES ===")
            schema_parts.extend([
                "1. Simple selection: SELECT * FROM customers WHERE country = 'USA'",
                "2. Join query: SELECT c.companyname, COUNT(o.orderid) FROM customers c LEFT JOIN orders o ON c.customerid = o.customerid GROUP BY c.companyname",
                "3. Aggregate: SELECT categoryname, AVG(unitprice) FROM products p JOIN categories c ON p.categoryid = c.categoryid GROUP BY categoryname",
                ""
            ])
            
            self.schema_context = "\n".join(schema_parts)
            logger.info(f"Schema context loaded for {len(table_names)} tables")
            
        except Exception as e:
            logger.error(f"Failed to load schema context: {str(e)}")
            self.schema_context = "Schema information unavailable"
    
    def _create_prompt(self, question: str) -> str:
        """Create optimized prompt for Gemini API."""
        prompt = f"""You are a PostgreSQL expert. Convert the following natural language question into a valid PostgreSQL SELECT query.

{self.schema_context}

=== IMPORTANT RULES ===
1. Generate ONLY SELECT queries (no INSERT, UPDATE, DELETE, DROP, etc.)
2. Use proper PostgreSQL syntax
3. Include appropriate JOINs when querying multiple tables
4. Use table aliases for readability
5. Return only the SQL query without explanations
6. Do not include semicolons at the end
7. Use ILIKE for case-insensitive text searches
8. Always use proper column names from the schema above

=== QUESTION ===
{question}

=== SQL QUERY ==="""
        
        return prompt
    
    @lru_cache(maxsize=100)
    def _cached_generate_sql(self, question_hash: str, question: str) -> str:
        """Generate SQL with caching based on question hash."""
        try:
            prompt = self._create_prompt(question)
            
            # Generate response using Gemini
            response = self.model.generate_content(prompt)
            
            if not response.text:
                raise ValueError("Empty response from Gemini API")
            
            # Extract SQL from response
            sql = response.text.strip()
            
            # Clean up the response (remove markdown formatting if present)
            if sql.startswith('```sql'):
                sql = sql[6:]
            if sql.startswith('```'):
                sql = sql[3:]
            if sql.endswith('```'):
                sql = sql[:-3]
            
            sql = sql.strip()
            
            logger.info(f"Generated SQL for question: {question[:50]}...")
            return sql
            
        except Exception as e:
            logger.error(f"Error generating SQL with Gemini: {str(e)}")
            raise
    
    def generate_sql(self, question: str) -> str:
        """Generate SQL query from natural language question."""
        if not question or not question.strip():
            raise ValueError("Question cannot be empty")
        
        # Create hash for caching
        question_hash = hashlib.md5(question.encode()).hexdigest()
        
        try:
            # Use cached generation
            sql = self._cached_generate_sql(question_hash, question)
            
            # Validate the generated SQL
            is_valid, cleaned_sql, error = self.validator.validate_query(sql)
            
            if not is_valid:
                logger.warning(f"Generated invalid SQL: {error}")
                raise ValueError(f"Generated SQL is invalid: {error}")
            
            # Validate table access
            table_names = list(self.table_info.keys())
            is_valid, error = self.validator.validate_table_access(cleaned_sql, table_names)
            
            if not is_valid:
                logger.warning(f"Generated SQL accesses invalid tables: {error}")
                raise ValueError(f"Generated SQL accesses invalid tables: {error}")
            
            return cleaned_sql
            
        except Exception as e:
            logger.error(f"Failed to generate SQL for question: {question}")
            raise
    
    def execute_question(self, question: str) -> Dict[str, Any]:
        """Execute natural language question and return results."""
        try:
            # Generate SQL
            sql = self.generate_sql(question)
            
            # Execute query
            results_df = self.db_manager.execute_query(sql, use_query_user=True)
            
            # Convert to JSON-serializable format
            results = results_df.to_dict('records')
            
            return {
                "question": question,
                "sql": sql,
                "results": results,
                "row_count": len(results),
                "success": True,
                "error": None
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to execute question: {error_msg}")
            
            return {
                "question": question,
                "sql": None,
                "results": [],
                "row_count": 0,
                "success": False,
                "error": error_msg
            }
    
    def explain_query(self, sql: str) -> Dict[str, Any]:
        """Get query execution plan and analysis."""
        try:
            # Get execution plan
            explain_sql = f"EXPLAIN (FORMAT JSON, ANALYZE FALSE) {sql}"
            plan_df = self.db_manager.execute_query(explain_sql, use_query_user=True)
            
            if not plan_df.empty:
                plan_json = plan_df.iloc[0, 0]
                
                return {
                    "sql": sql,
                    "execution_plan": plan_json,
                    "success": True,
                    "error": None
                }
            else:
                return {
                    "sql": sql,
                    "execution_plan": None,
                    "success": False,
                    "error": "No execution plan returned"
                }
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to explain query: {error_msg}")
            
            return {
                "sql": sql,
                "execution_plan": None,
                "success": False,
                "error": error_msg
            }
    
    def get_schema_info(self) -> Dict[str, Any]:
        """Get database schema information."""
        return {
            "tables": list(self.table_info.keys()),
            "table_details": self.table_info,
            "schema_context": self.schema_context
        }
    
    def clear_cache(self) -> None:
        """Clear the SQL generation cache."""
        self._cached_generate_sql.cache_clear()
        logger.info("SQL generation cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache statistics."""
        cache_info = self._cached_generate_sql.cache_info()
        return {
            "hits": cache_info.hits,
            "misses": cache_info.misses,
            "maxsize": cache_info.maxsize,
            "currsize": cache_info.currsize,
            "hit_rate": cache_info.hits / (cache_info.hits + cache_info.misses) if (cache_info.hits + cache_info.misses) > 0 else 0
        }
