#!/usr/bin/env python3
"""Database setup script for Text2SQL Analytics System."""

import sys
import os
import logging
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config import Config
from src.database import DatabaseManager
from src.data_loader import DataLoader
from src.utils import timing_decorator

# Setup logging
Config.setup_logging()
logger = logging.getLogger(__name__)


class DatabaseSetup:
    """Handles complete database setup process."""
    
    def __init__(self):
        """Initialize database setup."""
        self.data_loader = DataLoader()
        self.db_manager = DatabaseManager(use_query_user=False)  # Use admin connection
        
    @timing_decorator
    def validate_prerequisites(self) -> bool:
        """Validate that all prerequisites are met."""
        logger.info("Validating prerequisites...")
        
        # Check if Excel file exists
        excel_path = Config.get_data_path("northwind.xlsx")
        if not os.path.exists(excel_path):
            logger.error(f"Northwind Excel file not found: {excel_path}")
            logger.error("Please download the file from Maven Analytics and place it in data/raw/")
            return False
        
        # Validate configuration
        if not Config.validate_config():
            logger.error("Configuration validation failed")
            return False
        
        # Test database connection
        try:
            with self.db_manager.get_session() as session:
                session.execute("SELECT 1")
            logger.info("Database connection successful")
        except Exception as e:
            logger.error(f"Database connection failed: {str(e)}")
            return False
        
        logger.info("All prerequisites validated successfully")
        return True
    
    @timing_decorator
    def create_read_only_user(self) -> None:
        """Create read-only database user for query execution."""
        logger.info("Creating read-only database user...")
        
        try:
            # SQL to create read-only user
            create_user_sql = f"""
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{Config.DB_QUERY_USER}') THEN
                    CREATE ROLE {Config.DB_QUERY_USER} WITH LOGIN PASSWORD '{Config.DB_QUERY_PASSWORD}';
                END IF;
            END
            $$;
            
            -- Grant connect permission
            GRANT CONNECT ON DATABASE {Config.DB_NAME} TO {Config.DB_QUERY_USER};
            
            -- Grant usage on schema
            GRANT USAGE ON SCHEMA public TO {Config.DB_QUERY_USER};
            
            -- Grant select on all existing tables
            GRANT SELECT ON ALL TABLES IN SCHEMA public TO {Config.DB_QUERY_USER};
            
            -- Grant select on future tables
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO {Config.DB_QUERY_USER};
            
            -- Set statement timeout for safety
            ALTER ROLE {Config.DB_QUERY_USER} SET statement_timeout = '{Config.QUERY_TIMEOUT}s';
            """
            
            self.db_manager.execute_script(create_user_sql, use_query_user=False)
            logger.info(f"Read-only user '{Config.DB_QUERY_USER}' created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create read-only user: {str(e)}")
            raise
    
    @timing_decorator
    def create_schema(self) -> None:
        """Create database schema from DDL file."""
        logger.info("Creating database schema...")
        
        try:
            # Load schema DDL
            schema_path = Config.get_schema_path("schema.sql")
            
            if os.path.exists(schema_path):
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_ddl = f.read()
                logger.info(f"Loaded schema DDL from {schema_path}")
            else:
                # Generate schema DDL if file doesn't exist
                logger.info("Schema file not found, generating from data...")
                self.data_loader.load_excel_data()
                self.data_loader.validate_data_types()
                schema_ddl = self.data_loader.generate_schema_ddl()
                indexes_ddl = self.data_loader.generate_indexes_ddl()
                
                # Save to file
                with open(schema_path, 'w', encoding='utf-8') as f:
                    f.write(schema_ddl)
                    f.write("\n")
                    f.write(indexes_ddl)
                
                logger.info(f"Generated and saved schema DDL to {schema_path}")
            
            # Execute schema creation
            self.db_manager.execute_script(schema_ddl, use_query_user=False)
            logger.info("Database schema created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create schema: {str(e)}")
            raise
    
    @timing_decorator
    def load_data(self) -> None:
        """Load normalized data into database."""
        logger.info("Loading data into database...")
        
        try:
            # Run data normalization pipeline
            normalized_data = self.data_loader.run_full_pipeline()
            
            # Insert data in dependency order
            table_order = [
                'categories',
                'suppliers', 
                'shippers',
                'customers',
                'employees',
                'products',
                'orders',
                'order_details'
            ]
            
            for table_name in table_order:
                if table_name in normalized_data:
                    df = normalized_data[table_name]
                    logger.info(f"Inserting {len(df)} rows into {table_name}")
                    
                    # Drop audit columns if they exist (will be added by DB defaults)
                    df_insert = df.copy()
                    if 'created_at' in df_insert.columns:
                        df_insert = df_insert.drop('created_at', axis=1)
                    if 'updated_at' in df_insert.columns:
                        df_insert = df_insert.drop('updated_at', axis=1)
                    
                    self.db_manager.insert_dataframe(
                        df_insert, 
                        table_name, 
                        if_exists='replace',
                        batch_size=500
                    )
                    
                    logger.info(f"Successfully inserted data into {table_name}")
                else:
                    logger.warning(f"Table {table_name} not found in normalized data")
            
            logger.info("All data loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load data: {str(e)}")
            raise
    
    @timing_decorator
    def verify_installation(self) -> None:
        """Verify that the database setup was successful."""
        logger.info("Verifying database installation...")
        
        try:
            # Check that all tables exist and have data
            expected_tables = [
                'categories', 'suppliers', 'shippers', 'customers',
                'employees', 'products', 'orders', 'order_details'
            ]
            
            for table_name in expected_tables:
                if self.db_manager.table_exists(table_name):
                    # Count rows
                    count_df = self.db_manager.execute_query(
                        f"SELECT COUNT(*) as row_count FROM {table_name}",
                        use_query_user=False
                    )
                    row_count = count_df.iloc[0]['row_count']
                    logger.info(f"Table {table_name}: {row_count} rows")
                    
                    if row_count == 0:
                        logger.warning(f"Table {table_name} is empty")
                else:
                    logger.error(f"Table {table_name} does not exist")
                    raise RuntimeError(f"Missing table: {table_name}")
            
            # Test read-only user access
            try:
                query_db = DatabaseManager(use_query_user=True)
                test_df = query_db.execute_query("SELECT COUNT(*) FROM customers")
                logger.info(f"Read-only user test successful: {test_df.iloc[0, 0]} customers")
            except Exception as e:
                logger.error(f"Read-only user test failed: {str(e)}")
                raise
            
            logger.info("Database installation verified successfully")
            
        except Exception as e:
            logger.error(f"Database verification failed: {str(e)}")
            raise
    
    def run_setup(self) -> None:
        """Run complete database setup process."""
        logger.info("=== Starting Database Setup ===")
        
        try:
            # Step 1: Validate prerequisites
            if not self.validate_prerequisites():
                raise RuntimeError("Prerequisites validation failed")
            
            # Step 2: Create schema
            self.create_schema()
            
            # Step 3: Create read-only user
            self.create_read_only_user()
            
            # Step 4: Load data
            self.load_data()
            
            # Step 5: Verify installation
            self.verify_installation()
            
            logger.info("=== Database Setup Completed Successfully ===")
            
        except Exception as e:
            logger.error(f"Database setup failed: {str(e)}")
            raise
        finally:
            # Cleanup connections
            self.db_manager.close_connections()


def main():
    """Main entry point for database setup."""
    try:
        setup = DatabaseSetup()
        setup.run_setup()
        
        print("\n✅ Database setup completed successfully!")
        print("\nNext steps:")
        print("1. Run tests: pytest --cov=src tests/ --cov-report=html")
        print("2. Start API: uvicorn src.app:app --reload")
        print("3. Try some queries!")
        
    except Exception as e:
        print(f"\n❌ Database setup failed: {str(e)}")
        print("\nPlease check the logs and fix any issues before retrying.")
        sys.exit(1)


if __name__ == "__main__":
    main()
