"""Utility functions for Text2SQL Analytics System."""

import logging
import time
import functools
from typing import Any, Callable, Dict, List, Optional
import pandas as pd
from datetime import datetime
import json

logger = logging.getLogger(__name__)


def timing_decorator(func: Callable) -> Callable:
    """Decorator to measure function execution time."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"{func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.3f}s: {str(e)}")
            raise
    return wrapper


def log_function_call(func: Callable) -> Callable:
    """Decorator to log function calls with parameters."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Log function call (be careful with sensitive data)
        func_name = func.__name__
        logger.debug(f"Calling {func_name} with args={len(args)}, kwargs={list(kwargs.keys())}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func_name} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func_name} failed: {str(e)}")
            raise
    return wrapper


def safe_execute(func: Callable, default_return: Any = None, 
                log_errors: bool = True) -> Callable:
    """Safely execute a function with error handling."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if log_errors:
                logger.error(f"Safe execution failed for {func.__name__}: {str(e)}")
            return default_return
    return wrapper


class DataFrameUtils:
    """Utilities for DataFrame operations."""
    
    @staticmethod
    def safe_to_dict(df: pd.DataFrame, orient: str = 'records') -> List[Dict[str, Any]]:
        """Safely convert DataFrame to dictionary with proper type handling."""
        try:
            # Handle datetime columns
            df_copy = df.copy()
            for col in df_copy.columns:
                if pd.api.types.is_datetime64_any_dtype(df_copy[col]):
                    df_copy[col] = df_copy[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                elif pd.api.types.is_numeric_dtype(df_copy[col]):
                    # Handle NaN values
                    df_copy[col] = df_copy[col].where(pd.notnull(df_copy[col]), None)
            
            return df_copy.to_dict(orient)
            
        except Exception as e:
            logger.error(f"Error converting DataFrame to dict: {str(e)}")
            return []
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, required_columns: Optional[List[str]] = None) -> bool:
        """Validate DataFrame structure and content."""
        try:
            if df is None or df.empty:
                return False
            
            if required_columns:
                missing_cols = set(required_columns) - set(df.columns)
                if missing_cols:
                    logger.warning(f"Missing required columns: {missing_cols}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"DataFrame validation error: {str(e)}")
            return False
    
    @staticmethod
    def clean_column_names(df: pd.DataFrame) -> pd.DataFrame:
        """Clean DataFrame column names for database compatibility."""
        df_copy = df.copy()
        df_copy.columns = [
            col.lower().replace(' ', '_').replace('-', '_').replace('.', '_')
            for col in df_copy.columns
        ]
        return df_copy


class QueryMetrics:
    """Track and analyze query performance metrics."""
    
    def __init__(self):
        self.metrics = []
    
    def record_query(self, question: str, sql: str, execution_time: float, 
                    row_count: int, success: bool, error: Optional[str] = None):
        """Record query execution metrics."""
        metric = {
            'timestamp': datetime.now().isoformat(),
            'question': question,
            'sql': sql,
            'execution_time': execution_time,
            'row_count': row_count,
            'success': success,
            'error': error
        }
        self.metrics.append(metric)
        
        # Keep only last 1000 metrics
        if len(self.metrics) > 1000:
            self.metrics = self.metrics[-1000:]
    
    def get_success_rate(self) -> float:
        """Calculate query success rate."""
        if not self.metrics:
            return 0.0
        
        successful = sum(1 for m in self.metrics if m['success'])
        return successful / len(self.metrics)
    
    def get_average_execution_time(self) -> float:
        """Calculate average execution time for successful queries."""
        successful_metrics = [m for m in self.metrics if m['success']]
        if not successful_metrics:
            return 0.0
        
        total_time = sum(m['execution_time'] for m in successful_metrics)
        return total_time / len(successful_metrics)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of query metrics."""
        if not self.metrics:
            return {
                'total_queries': 0,
                'success_rate': 0.0,
                'average_execution_time': 0.0,
                'average_row_count': 0.0
            }
        
        successful_metrics = [m for m in self.metrics if m['success']]
        
        return {
            'total_queries': len(self.metrics),
            'successful_queries': len(successful_metrics),
            'success_rate': self.get_success_rate(),
            'average_execution_time': self.get_average_execution_time(),
            'average_row_count': sum(m['row_count'] for m in successful_metrics) / len(successful_metrics) if successful_metrics else 0.0,
            'common_errors': self._get_common_errors()
        }
    
    def _get_common_errors(self) -> List[Dict[str, Any]]:
        """Get most common error types."""
        error_counts = {}
        for metric in self.metrics:
            if not metric['success'] and metric['error']:
                error_type = metric['error'].split(':')[0]  # Get error type
                error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        # Sort by frequency
        sorted_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        return [{'error_type': error, 'count': count} for error, count in sorted_errors[:5]]


class ConfigValidator:
    """Validate configuration settings."""
    
    @staticmethod
    def validate_database_config() -> bool:
        """Validate database configuration."""
        from .config import Config
        
        required_vars = [
            Config.DB_HOST,
            Config.DB_PORT,
            Config.DB_NAME,
            Config.DB_USER,
            Config.DB_PASSWORD
        ]
        
        return all(var for var in required_vars)
    
    @staticmethod
    def validate_api_config() -> bool:
        """Validate API configuration."""
        from .config import Config
        
        return bool(Config.GEMINI_API_KEY)
    
    @staticmethod
    def validate_all_config() -> Dict[str, bool]:
        """Validate all configuration settings."""
        return {
            'database': ConfigValidator.validate_database_config(),
            'api': ConfigValidator.validate_api_config()
        }


class JSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling special data types."""
    
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        return super().default(obj)


def format_sql_query(sql: str) -> str:
    """Format SQL query for better readability."""
    try:
        import sqlparse
        return sqlparse.format(sql, reindent=True, keyword_case='upper')
    except ImportError:
        logger.warning("sqlparse not available, returning unformatted SQL")
        return sql


def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate string to specified length."""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    import re
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    # Remove leading/trailing underscores and dots
    sanitized = sanitized.strip('_.')
    return sanitized or 'unnamed_file'
