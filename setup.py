"""Setup configuration for text2sql-analytics package."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="text2sql-analytics",
    version="1.0.0",
    author="Makebell Inc. Backend Engineer Assessment",
    author_email="<EMAIL>",
    description="Production-ready Text2SQL Analytics System with PostgreSQL",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/raisulislam0/text2sql.git",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.1",
        ],
    },
    entry_points={
        "console_scripts": [
            "text2sql-setup=scripts.setup_database:main",
            "text2sql-eval=scripts.run_evaluation:main",
        ],
    },
)
