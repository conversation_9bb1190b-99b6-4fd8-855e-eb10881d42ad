"""Query validation and security module for Text2SQL Analytics System."""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
import sqlparse
from sqlparse.sql import Statement, Token
from sqlparse.tokens import Keyword, Name

from .config import Config

logger = logging.getLogger(__name__)


class QueryValidator:
    """Validates and sanitizes SQL queries for security and compliance."""
    
    # Allowed SQL keywords for SELECT operations
    ALLOWED_KEYWORDS = {
        'SELECT', 'FROM', 'WHERE', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'OUTER',
        'ON', 'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN', 'LIKE', 'ILIKE',
        'GROUP', 'BY', 'HAVING', 'ORDER', 'ASC', 'DESC', 'LIMIT', 'OFFSET',
        'DISTINCT', 'AS', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'NULL',
        'IS', 'TRUE', 'FALSE', 'UNION', 'ALL', 'EXCEPT', 'INTERSECT',
        'WITH', 'RECURSIVE', 'CTE'
    }
    
    # Aggregate and window functions
    ALLOWED_FUNCTIONS = {
        'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'STDDEV', 'VARIANCE',
        'ROW_NUMBER', 'RANK', 'DENSE_RANK', 'LAG', 'LEAD',
        'FIRST_VALUE', 'LAST_VALUE', 'NTH_VALUE',
        'UPPER', 'LOWER', 'TRIM', 'LENGTH', 'SUBSTRING', 'CONCAT',
        'COALESCE', 'NULLIF', 'GREATEST', 'LEAST',
        'EXTRACT', 'DATE_PART', 'NOW', 'CURRENT_DATE', 'CURRENT_TIMESTAMP',
        'CAST', 'CONVERT', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER'
    }
    
    # Dangerous patterns that should be blocked
    DANGEROUS_PATTERNS = [
        r';\s*DROP\s+',
        r';\s*DELETE\s+',
        r';\s*UPDATE\s+',
        r';\s*INSERT\s+',
        r';\s*CREATE\s+',
        r';\s*ALTER\s+',
        r';\s*TRUNCATE\s+',
        r'--.*$',  # SQL comments
        r'/\*.*?\*/',  # Multi-line comments
        r'xp_cmdshell',
        r'sp_executesql',
        r'EXEC\s*\(',
        r'EXECUTE\s*\(',
        r'pg_sleep',
        r'pg_read_file',
        r'pg_ls_dir',
        r'information_schema\..*',
        r'pg_catalog\..*',
        r'pg_proc',
        r'pg_user'
    ]
    
    def __init__(self):
        """Initialize QueryValidator."""
        self.max_query_length = 10000  # Maximum query length
        self.max_tables_per_query = 10  # Maximum tables in a single query
        
    def validate_query(self, sql: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate SQL query for security and compliance.
        
        Returns:
            Tuple of (is_valid, cleaned_sql, error_message)
        """
        try:
            # Step 1: Basic validation
            is_valid, error = self._basic_validation(sql)
            if not is_valid:
                return False, sql, error
            
            # Step 2: Parse and analyze SQL
            parsed = sqlparse.parse(sql)
            if not parsed:
                return False, sql, "Unable to parse SQL query"
            
            statement = parsed[0]
            
            # Step 3: Check for dangerous patterns
            is_valid, error = self._check_dangerous_patterns(sql)
            if not is_valid:
                return False, sql, error
            
            # Step 4: Validate statement type
            is_valid, error = self._validate_statement_type(statement)
            if not is_valid:
                return False, sql, error
            
            # Step 5: Validate keywords and functions
            is_valid, error = self._validate_keywords_and_functions(statement)
            if not is_valid:
                return False, sql, error
            
            # Step 6: Clean and format query
            cleaned_sql = self._clean_query(sql)
            
            # Step 7: Add safety limits
            final_sql = self._add_safety_limits(cleaned_sql)
            
            logger.info("Query validation successful")
            return True, final_sql, None
            
        except Exception as e:
            logger.error(f"Query validation error: {str(e)}")
            return False, sql, f"Validation error: {str(e)}"
    
    def _basic_validation(self, sql: str) -> Tuple[bool, Optional[str]]:
        """Perform basic validation checks."""
        if not sql or not sql.strip():
            return False, "Empty query"
        
        if len(sql) > self.max_query_length:
            return False, f"Query too long (max {self.max_query_length} characters)"
        
        # Check for multiple statements (semicolon followed by non-whitespace)
        if re.search(r';\s*\w', sql):
            return False, "Multiple statements not allowed"
        
        return True, None
    
    def _check_dangerous_patterns(self, sql: str) -> Tuple[bool, Optional[str]]:
        """Check for dangerous SQL patterns."""
        sql_upper = sql.upper()
        
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, sql_upper, re.IGNORECASE | re.MULTILINE):
                return False, f"Dangerous pattern detected: {pattern}"
        
        return True, None
    
    def _validate_statement_type(self, statement: Statement) -> Tuple[bool, Optional[str]]:
        """Validate that the statement is a SELECT query."""
        # Get the first meaningful token
        first_token = None
        for token in statement.flatten():
            if token.ttype is Keyword and token.value.upper().strip():
                first_token = token.value.upper().strip()
                break
        
        if first_token != 'SELECT':
            return False, f"Only SELECT statements allowed, found: {first_token}"
        
        # Check for forbidden keywords in the statement
        forbidden_keywords = {
            'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER', 
            'TRUNCATE', 'GRANT', 'REVOKE', 'EXEC', 'EXECUTE'
        }
        
        for token in statement.flatten():
            if token.ttype is Keyword:
                keyword = token.value.upper().strip()
                if keyword in forbidden_keywords:
                    return False, f"Forbidden keyword: {keyword}"
        
        return True, None
    
    def _validate_keywords_and_functions(self, statement: Statement) -> Tuple[bool, Optional[str]]:
        """Validate that only allowed keywords and functions are used."""
        for token in statement.flatten():
            if token.ttype is Keyword:
                keyword = token.value.upper().strip()
                if keyword and keyword not in self.ALLOWED_KEYWORDS:
                    # Check if it's an allowed function
                    if keyword not in self.ALLOWED_FUNCTIONS:
                        return False, f"Disallowed keyword/function: {keyword}"
        
        return True, None
    
    def _clean_query(self, sql: str) -> str:
        """Clean and normalize the SQL query."""
        # Remove comments
        sql = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
        sql = re.sub(r'/\*.*?\*/', '', sql, flags=re.DOTALL)
        
        # Normalize whitespace
        sql = re.sub(r'\s+', ' ', sql)
        sql = sql.strip()
        
        # Remove trailing semicolon if present
        if sql.endswith(';'):
            sql = sql[:-1]
        
        return sql
    
    def _add_safety_limits(self, sql: str) -> str:
        """Add safety limits to the query."""
        sql_upper = sql.upper()
        
        # Add LIMIT if not present
        if 'LIMIT' not in sql_upper:
            sql += f' LIMIT {Config.MAX_ROWS}'
        
        return sql
    
    def extract_table_names(self, sql: str) -> List[str]:
        """Extract table names from SQL query."""
        try:
            parsed = sqlparse.parse(sql)
            if not parsed:
                return []
            
            statement = parsed[0]
            tables = []
            
            # Simple extraction - look for tokens after FROM and JOIN
            tokens = list(statement.flatten())
            
            for i, token in enumerate(tokens):
                if token.ttype is Keyword:
                    keyword = token.value.upper().strip()
                    if keyword in ['FROM', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL']:
                        # Look for the next name token
                        for j in range(i + 1, len(tokens)):
                            next_token = tokens[j]
                            if next_token.ttype is Name:
                                table_name = next_token.value.strip()
                                if table_name and table_name not in tables:
                                    tables.append(table_name)
                                break
                            elif next_token.ttype is Keyword:
                                break
            
            return tables
            
        except Exception as e:
            logger.warning(f"Could not extract table names: {str(e)}")
            return []
    
    def validate_table_access(self, sql: str, allowed_tables: List[str]) -> Tuple[bool, Optional[str]]:
        """Validate that query only accesses allowed tables."""
        try:
            tables_in_query = self.extract_table_names(sql)
            
            if len(tables_in_query) > self.max_tables_per_query:
                return False, f"Too many tables in query (max {self.max_tables_per_query})"
            
            for table in tables_in_query:
                if table.lower() not in [t.lower() for t in allowed_tables]:
                    return False, f"Access to table '{table}' not allowed"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Table access validation error: {str(e)}")
            return False, f"Table validation error: {str(e)}"
    
    def is_safe_query(self, sql: str, allowed_tables: Optional[List[str]] = None) -> bool:
        """Quick check if query is safe to execute."""
        try:
            is_valid, _, error = self.validate_query(sql)
            if not is_valid:
                return False
            
            if allowed_tables:
                is_valid, error = self.validate_table_access(sql, allowed_tables)
                if not is_valid:
                    return False
            
            return True
            
        except Exception:
            return False
