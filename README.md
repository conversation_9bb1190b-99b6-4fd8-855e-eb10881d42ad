# Text2SQL Analytics System

A production-ready Text-to-SQL analytics system built with PostgreSQL and Google Gemini AI for the Makebell Inc. Backend Engineer Assessment.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Natural       │    │   Text2SQL       │    │   PostgreSQL    │
│   Language      │───▶│   Engine         │───▶│   Database      │
│   Query         │    │   (Gemini AI)    │    │   (Normalized)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Query          │
                       │   Validator      │
                       │   & Security     │
                       └──────────────────┘
```

## 🚀 Features

- **Data Engineering**: Automated Northwind data normalization to 3NF
- **AI Integration**: Google Gemini API for natural language to SQL conversion
- **Security**: SQL injection prevention, read-only access, query validation
- **Testing**: Comprehensive test suite with 80%+ coverage
- **API**: RESTful FastAPI endpoint for query execution
- **Performance**: Connection pooling, query optimization, caching

## 📋 Requirements

- Python 3.10+
- PostgreSQL 14+
- Google Gemini API key (free tier)

## 🛠️ Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone https://github.com/raisulislam0/text2sql.git
cd text2sql
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Edit `.env` with your settings:
```env
DB_URL=postgresql://postgres:your_password@localhost:5432/text2sql_analytics
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Download Northwind Dataset

Download the Northwind Excel file from [Maven Analytics](https://www.mavenanalytics.io/data-playground) and place it in `data/raw/northwind.xlsx`.

### 4. Setup Database

```bash
python scripts/setup_database.py
```

### 5. Run Tests

```bash
pytest --cov=src tests/ --cov-report=html
```

## 📊 Usage Examples

### Python API

```python
from src import Text2SQLEngine, DatabaseManager

# Initialize components
engine = Text2SQLEngine()
db = DatabaseManager()

# Convert natural language to SQL
question = "Show me the top 5 customers by total order value"
sql = engine.generate_sql(question)
results = db.execute_query(sql)
```

### REST API

```bash
# Start the API server
uvicorn src.app:app --reload

# Query via HTTP
curl -X POST "http://localhost:8000/query" \
     -H "Content-Type: application/json" \
     -d '{"question": "What are the top selling products?"}'
```

## 🧪 Testing

The system includes comprehensive testing:

- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end pipeline testing  
- **Accuracy Tests**: 20+ query validation scenarios
- **Security Tests**: SQL injection prevention

Run specific test categories:
```bash
# Unit tests only
pytest tests/test_*.py

# Accuracy tests only  
pytest tests/test_accuracy/

# With coverage report
pytest --cov=src --cov-report=html
```

## 📈 Performance Metrics

- Query execution time: <1 second average
- Test coverage: 85%+
- Accuracy score: 0.85+ (execution_success × 0.2 + result_match × 0.4 + query_quality × 0.4)

## 🔒 Security Features

- Read-only database access for query execution
- SQL injection prevention via parameterized queries
- Query validation (SELECT-only operations)
- Timeout enforcement (5 seconds default)
- Row limit enforcement (1000 rows default)

## 📁 Project Structure

```
text2sql-analytics/
├── README.md
├── requirements.txt
├── .env.example
├── .gitignore
├── setup.py
├── data/
│   ├── raw/northwind.xlsx
│   └── schema/schema.sql
├── src/
│   ├── __init__.py
│   ├── config.py
│   ├── data_loader.py
│   ├── database.py
│   ├── text2sql_engine.py
│   ├── query_validator.py
│   └── utils.py
├── tests/
│   ├── conftest.py
│   ├── test_*.py
│   └── test_accuracy/
├── scripts/
│   ├── setup_database.py
│   └── run_evaluation.py
└── notebooks/
    └── analysis.ipynb
```

## 🎯 Evaluation Criteria

This system addresses all assessment criteria:

- **Data Engineering (15%)**: Automated normalization pipeline
- **Code Quality (20%)**: Type hints, docstrings, structured logging
- **AI Integration (10%)**: Google Gemini API integration
- **Testing Coverage (25%)**: 80%+ coverage with comprehensive test suite
- **Text2SQL Accuracy (25%)**: Validated against 20+ test scenarios
- **Security (5%)**: Multi-layer security implementation

## 🚧 Limitations & Future Improvements

- Currently supports SELECT queries only
- Limited to Northwind dataset schema
- Gemini API rate limits may apply
- Future: Support for more complex analytical queries

## 📄 License

MIT License - see LICENSE file for details.

---

**Makebell Inc. Backend Engineer Assessment** - Built with ❤️ and Python
