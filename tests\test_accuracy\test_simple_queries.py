"""Simple query accuracy tests for Text2SQL engine."""

import pytest
import pandas as pd
from unittest.mock import patch, Mock
import sqlparse
import time

from src.text2sql_engine import Text2SQLEngine
from src.query_validator import QueryValidator


class TestSimpleQueries:
    """Test simple SELECT queries for accuracy."""
    
    @pytest.fixture
    def accuracy_metrics(self):
        """Fixture for accuracy metrics calculation."""
        def calculate_accuracy(execution_success, result_match, query_quality):
            return 0.2 * execution_success + 0.4 * result_match + 0.4 * query_quality
        return calculate_accuracy
    
    def test_select_all_customers(self, mock_text2sql_engine, mock_database_manager, accuracy_metrics):
        """Test: Show all customers."""
        question = "Show all customers"
        expected_sql_pattern = "SELECT * FROM customers"
        
        # Mock Gemini response
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM customers"
            mock_generate.return_value = mock_response
            
            # Execute question
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            # Metrics calculation
            execution_success = 1.0 if result['success'] else 0.0
            
            # Check if SQL contains expected pattern
            result_match = 1.0 if result['sql'] and 'customers' in result['sql'].lower() else 0.0
            
            # Query quality checks
            quality_score = 0.0
            if result['sql']:
                parsed = sqlparse.parse(result['sql'])
                if parsed and len(parsed) > 0:
                    quality_score += 0.3  # Valid SQL structure
                if 'SELECT' in result['sql'].upper():
                    quality_score += 0.3  # Uses SELECT
                if execution_time < 1.0:
                    quality_score += 0.4  # Fast execution
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            # Assertions
            assert result['success'], f"Query should succeed: {result.get('error')}"
            assert result['sql'] is not None, "SQL should be generated"
            assert 'customers' in result['sql'].lower(), "SQL should reference customers table"
            assert accuracy_score >= 0.8, f"Accuracy score {accuracy_score} should be >= 0.8"
    
    def test_select_all_products(self, mock_text2sql_engine, accuracy_metrics):
        """Test: List all products."""
        question = "List all products"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM products"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            # Metrics
            execution_success = 1.0 if result['success'] else 0.0
            result_match = 1.0 if result['sql'] and 'products' in result['sql'].lower() else 0.0
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.3
                if 'SELECT' in result['sql'].upper():
                    quality_score += 0.3
                if execution_time < 1.0:
                    quality_score += 0.4
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_select_all_orders(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show all orders."""
        question = "Show all orders"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT * FROM orders"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            result_match = 1.0 if result['sql'] and 'orders' in result['sql'].lower() else 0.0
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.3
                if 'SELECT' in result['sql'].upper():
                    quality_score += 0.3
                if execution_time < 1.0:
                    quality_score += 0.4
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'orders' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_select_customer_names(self, mock_text2sql_engine, accuracy_metrics):
        """Test: Show customer names."""
        question = "Show customer names"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT companyname FROM customers"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            result_match = 1.0 if (result['sql'] and 'customers' in result['sql'].lower() 
                                 and ('companyname' in result['sql'].lower() or 'name' in result['sql'].lower())) else 0.0
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.3
                if 'SELECT' in result['sql'].upper() and '*' not in result['sql']:
                    quality_score += 0.3  # Specific column selection
                if execution_time < 1.0:
                    quality_score += 0.4
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'customers' in result['sql'].lower()
            assert accuracy_score >= 0.8
    
    def test_select_product_names(self, mock_text2sql_engine, accuracy_metrics):
        """Test: List product names."""
        question = "List product names"
        
        with patch.object(mock_text2sql_engine.model, 'generate_content') as mock_generate:
            mock_response = Mock()
            mock_response.text = "SELECT productname FROM products"
            mock_generate.return_value = mock_response
            
            start_time = time.time()
            result = mock_text2sql_engine.execute_question(question)
            execution_time = time.time() - start_time
            
            execution_success = 1.0 if result['success'] else 0.0
            result_match = 1.0 if (result['sql'] and 'products' in result['sql'].lower() 
                                 and ('productname' in result['sql'].lower() or 'name' in result['sql'].lower())) else 0.0
            
            quality_score = 0.0
            if result['sql']:
                if sqlparse.parse(result['sql']):
                    quality_score += 0.3
                if 'SELECT' in result['sql'].upper() and '*' not in result['sql']:
                    quality_score += 0.3
                if execution_time < 1.0:
                    quality_score += 0.4
            
            accuracy_score = accuracy_metrics(execution_success, result_match, quality_score)
            
            assert result['success']
            assert 'products' in result['sql'].lower()
            assert accuracy_score >= 0.8


class TestSimpleQueryValidation:
    """Test validation of simple queries."""
    
    def test_simple_query_security(self, query_validator):
        """Test that simple queries pass security validation."""
        simple_queries = [
            "SELECT * FROM customers",
            "SELECT * FROM products", 
            "SELECT * FROM orders",
            "SELECT companyname FROM customers",
            "SELECT productname FROM products"
        ]
        
        for sql in simple_queries:
            is_valid, cleaned_sql, error = query_validator.validate_query(sql)
            assert is_valid, f"Simple query should be valid: {sql}, Error: {error}"
            assert cleaned_sql is not None
            assert 'LIMIT' in cleaned_sql.upper()  # Should add LIMIT
    
    def test_simple_query_table_extraction(self, query_validator):
        """Test table name extraction from simple queries."""
        test_cases = [
            ("SELECT * FROM customers", ["customers"]),
            ("SELECT * FROM products", ["products"]),
            ("SELECT * FROM orders", ["orders"]),
            ("SELECT name FROM users", ["users"])
        ]
        
        for sql, expected_tables in test_cases:
            tables = query_validator.extract_table_names(sql)
            assert len(tables) == len(expected_tables)
            for expected_table in expected_tables:
                assert expected_table in tables


class TestSimpleQueryMetrics:
    """Test metrics calculation for simple queries."""
    
    def test_execution_success_metric(self):
        """Test execution success metric calculation."""
        # Successful execution
        assert self._calculate_execution_success(True) == 1.0
        
        # Failed execution
        assert self._calculate_execution_success(False) == 0.0
    
    def test_result_match_metric(self):
        """Test result match metric calculation."""
        # Perfect match
        expected_sql = "SELECT * FROM customers"
        actual_sql = "SELECT * FROM customers"
        assert self._calculate_result_match(expected_sql, actual_sql) == 1.0
        
        # Partial match (same table)
        expected_sql = "SELECT * FROM customers"
        actual_sql = "SELECT companyname FROM customers"
        assert self._calculate_result_match(expected_sql, actual_sql) >= 0.5
        
        # No match
        expected_sql = "SELECT * FROM customers"
        actual_sql = "SELECT * FROM products"
        assert self._calculate_result_match(expected_sql, actual_sql) < 0.5
    
    def test_query_quality_metric(self):
        """Test query quality metric calculation."""
        # High quality query
        sql = "SELECT companyname FROM customers"
        execution_time = 0.1
        quality = self._calculate_query_quality(sql, execution_time)
        assert quality >= 0.8
        
        # Lower quality query (slow execution)
        sql = "SELECT * FROM customers"
        execution_time = 2.0
        quality = self._calculate_query_quality(sql, execution_time)
        assert quality < 0.8
    
    def test_overall_accuracy_score(self):
        """Test overall accuracy score calculation."""
        # Perfect score
        execution_success = 1.0
        result_match = 1.0
        query_quality = 1.0
        
        accuracy = 0.2 * execution_success + 0.4 * result_match + 0.4 * query_quality
        assert accuracy == 1.0
        
        # Minimum passing score
        execution_success = 1.0
        result_match = 0.8
        query_quality = 0.8
        
        accuracy = 0.2 * execution_success + 0.4 * result_match + 0.4 * query_quality
        assert accuracy >= 0.8
    
    def _calculate_execution_success(self, success: bool) -> float:
        """Calculate execution success metric."""
        return 1.0 if success else 0.0
    
    def _calculate_result_match(self, expected: str, actual: str) -> float:
        """Calculate result match metric."""
        if not expected or not actual:
            return 0.0
        
        expected_lower = expected.lower()
        actual_lower = actual.lower()
        
        # Extract table names
        expected_tables = set()
        actual_tables = set()
        
        for word in expected_lower.split():
            if word in ['customers', 'products', 'orders', 'categories', 'suppliers']:
                expected_tables.add(word)
        
        for word in actual_lower.split():
            if word in ['customers', 'products', 'orders', 'categories', 'suppliers']:
                actual_tables.add(word)
        
        if expected_tables == actual_tables:
            return 1.0
        elif expected_tables & actual_tables:  # Some overlap
            return 0.5
        else:
            return 0.0
    
    def _calculate_query_quality(self, sql: str, execution_time: float) -> float:
        """Calculate query quality metric."""
        quality = 0.0
        
        if sql:
            # Valid SQL structure
            try:
                parsed = sqlparse.parse(sql)
                if parsed and len(parsed) > 0:
                    quality += 0.3
            except:
                pass
            
            # Uses SELECT
            if 'SELECT' in sql.upper():
                quality += 0.3
            
            # Fast execution
            if execution_time < 1.0:
                quality += 0.4
            elif execution_time < 2.0:
                quality += 0.2
        
        return min(quality, 1.0)
