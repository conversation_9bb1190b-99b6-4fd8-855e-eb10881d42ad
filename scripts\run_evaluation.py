#!/usr/bin/env python3
"""Evaluation script for Text2SQL Analytics System."""

import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config import Config
from src.database import DatabaseManager
from src.text2sql_engine import Text2SQLEngine
from src.query_validator import QueryValidator
from src.utils import QueryMetrics, timing_decorator

# Setup logging
Config.setup_logging()
import logging
logger = logging.getLogger(__name__)


class Text2SQLEvaluator:
    """Comprehensive evaluation system for Text2SQL accuracy and performance."""
    
    def __init__(self):
        """Initialize evaluator components."""
        self.db_manager = DatabaseManager(use_query_user=True)
        self.text2sql_engine = Text2SQLEngine(self.db_manager)
        self.query_validator = QueryValidator()
        self.query_metrics = QueryMetrics()
        
        # Test cases for evaluation
        self.test_cases = self._load_test_cases()
        self.results = []
        
    def _load_test_cases(self) -> List[Dict[str, Any]]:
        """Load comprehensive test cases for evaluation."""
        return [
            # Simple queries (5 cases)
            {
                'category': 'simple',
                'question': 'Show all customers',
                'expected_tables': ['customers'],
                'complexity': 1,
                'expected_operations': ['SELECT']
            },
            {
                'category': 'simple',
                'question': 'List all products',
                'expected_tables': ['products'],
                'complexity': 1,
                'expected_operations': ['SELECT']
            },
            {
                'category': 'simple',
                'question': 'Show all orders',
                'expected_tables': ['orders'],
                'complexity': 1,
                'expected_operations': ['SELECT']
            },
            {
                'category': 'simple',
                'question': 'Display customer names',
                'expected_tables': ['customers'],
                'complexity': 1,
                'expected_operations': ['SELECT']
            },
            {
                'category': 'simple',
                'question': 'List product names',
                'expected_tables': ['products'],
                'complexity': 1,
                'expected_operations': ['SELECT']
            },
            
            # Intermediate queries (10 cases)
            {
                'category': 'intermediate',
                'question': 'Show customers from Germany',
                'expected_tables': ['customers'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'WHERE']
            },
            {
                'category': 'intermediate',
                'question': 'Count total customers',
                'expected_tables': ['customers'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'COUNT']
            },
            {
                'category': 'intermediate',
                'question': 'Show products with price greater than 20',
                'expected_tables': ['products'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'WHERE']
            },
            {
                'category': 'intermediate',
                'question': 'List orders from 1996',
                'expected_tables': ['orders'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'WHERE']
            },
            {
                'category': 'intermediate',
                'question': 'Count products by category',
                'expected_tables': ['products'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'COUNT', 'GROUP BY']
            },
            {
                'category': 'intermediate',
                'question': 'Show top 10 most expensive products',
                'expected_tables': ['products'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'ORDER BY', 'LIMIT']
            },
            {
                'category': 'intermediate',
                'question': 'Calculate average product price',
                'expected_tables': ['products'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'AVG']
            },
            {
                'category': 'intermediate',
                'question': 'Show customers who have placed orders',
                'expected_tables': ['customers', 'orders'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'JOIN']
            },
            {
                'category': 'intermediate',
                'question': 'List products ordered by price descending',
                'expected_tables': ['products'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'ORDER BY']
            },
            {
                'category': 'intermediate',
                'question': 'Show orders with freight cost greater than 50',
                'expected_tables': ['orders'],
                'complexity': 2,
                'expected_operations': ['SELECT', 'WHERE']
            },
            
            # Complex queries (5 cases)
            {
                'category': 'complex',
                'question': 'Show customer names and their total order counts',
                'expected_tables': ['customers', 'orders'],
                'complexity': 3,
                'expected_operations': ['SELECT', 'JOIN', 'COUNT', 'GROUP BY']
            },
            {
                'category': 'complex',
                'question': 'Show top 5 products by total revenue',
                'expected_tables': ['products', 'order_details'],
                'complexity': 3,
                'expected_operations': ['SELECT', 'JOIN', 'SUM', 'GROUP BY', 'ORDER BY', 'LIMIT']
            },
            {
                'category': 'complex',
                'question': 'Show monthly sales totals for 1996',
                'expected_tables': ['orders', 'order_details'],
                'complexity': 3,
                'expected_operations': ['SELECT', 'JOIN', 'SUM', 'WHERE', 'GROUP BY']
            },
            {
                'category': 'complex',
                'question': 'Show employees and their sales performance',
                'expected_tables': ['employees', 'orders', 'order_details'],
                'complexity': 3,
                'expected_operations': ['SELECT', 'JOIN', 'SUM', 'GROUP BY']
            },
            {
                'category': 'complex',
                'question': 'Show products with revenue greater than average',
                'expected_tables': ['products', 'order_details'],
                'complexity': 3,
                'expected_operations': ['SELECT', 'JOIN', 'SUM', 'GROUP BY', 'HAVING']
            }
        ]
    
    @timing_decorator
    def run_evaluation(self) -> Dict[str, Any]:
        """Run comprehensive evaluation of Text2SQL system."""
        logger.info("Starting Text2SQL system evaluation...")
        
        evaluation_results = {
            'timestamp': datetime.now().isoformat(),
            'total_test_cases': len(self.test_cases),
            'results_by_category': {},
            'overall_metrics': {},
            'failed_queries': [],
            'performance_metrics': {},
            'recommendations': []
        }
        
        # Run tests for each category
        for category in ['simple', 'intermediate', 'complex']:
            category_cases = [tc for tc in self.test_cases if tc['category'] == category]
            category_results = self._evaluate_category(category, category_cases)
            evaluation_results['results_by_category'][category] = category_results
        
        # Calculate overall metrics
        evaluation_results['overall_metrics'] = self._calculate_overall_metrics()
        
        # Analyze failed queries
        evaluation_results['failed_queries'] = self._analyze_failed_queries()
        
        # Performance analysis
        evaluation_results['performance_metrics'] = self._analyze_performance()
        
        # Generate recommendations
        evaluation_results['recommendations'] = self._generate_recommendations(evaluation_results)
        
        # Save results
        self._save_evaluation_results(evaluation_results)
        
        logger.info("Text2SQL evaluation completed")
        return evaluation_results
    
    def _evaluate_category(self, category: str, test_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Evaluate test cases for a specific category."""
        logger.info(f"Evaluating {category} queries ({len(test_cases)} cases)")
        
        category_results = {
            'total_cases': len(test_cases),
            'successful_cases': 0,
            'accuracy_scores': [],
            'execution_times': [],
            'failed_cases': []
        }
        
        for i, test_case in enumerate(test_cases):
            logger.info(f"Testing {category} query {i+1}/{len(test_cases)}: {test_case['question']}")
            
            try:
                # Execute the question
                start_time = time.time()
                result = self.text2sql_engine.execute_question(test_case['question'])
                execution_time = time.time() - start_time
                
                # Calculate metrics
                execution_success = 1.0 if result['success'] else 0.0
                result_match = self._calculate_result_match(test_case, result)
                query_quality = self._calculate_query_quality(result['sql'], execution_time, test_case)
                
                # Overall accuracy score
                accuracy_score = 0.2 * execution_success + 0.4 * result_match + 0.4 * query_quality
                
                # Record metrics
                self.query_metrics.record_query(
                    question=test_case['question'],
                    sql=result['sql'],
                    execution_time=execution_time,
                    row_count=result['row_count'],
                    success=result['success'],
                    error=result['error']
                )
                
                # Store results
                test_result = {
                    'question': test_case['question'],
                    'category': category,
                    'success': result['success'],
                    'sql': result['sql'],
                    'execution_time': execution_time,
                    'accuracy_score': accuracy_score,
                    'execution_success': execution_success,
                    'result_match': result_match,
                    'query_quality': query_quality,
                    'error': result['error']
                }
                
                self.results.append(test_result)
                
                if result['success']:
                    category_results['successful_cases'] += 1
                    category_results['accuracy_scores'].append(accuracy_score)
                    category_results['execution_times'].append(execution_time)
                else:
                    category_results['failed_cases'].append({
                        'question': test_case['question'],
                        'error': result['error'],
                        'accuracy_score': accuracy_score
                    })
                
            except Exception as e:
                logger.error(f"Error evaluating query: {str(e)}")
                category_results['failed_cases'].append({
                    'question': test_case['question'],
                    'error': str(e),
                    'accuracy_score': 0.0
                })
        
        # Calculate category statistics
        if category_results['accuracy_scores']:
            category_results['average_accuracy'] = sum(category_results['accuracy_scores']) / len(category_results['accuracy_scores'])
            category_results['average_execution_time'] = sum(category_results['execution_times']) / len(category_results['execution_times'])
        else:
            category_results['average_accuracy'] = 0.0
            category_results['average_execution_time'] = 0.0
        
        category_results['success_rate'] = category_results['successful_cases'] / category_results['total_cases']
        
        return category_results
    
    def _calculate_result_match(self, test_case: Dict[str, Any], result: Dict[str, Any]) -> float:
        """Calculate how well the result matches expectations."""
        if not result['success'] or not result['sql']:
            return 0.0
        
        match_score = 0.0
        sql_upper = result['sql'].upper()
        
        # Check if expected tables are referenced
        expected_tables = test_case.get('expected_tables', [])
        tables_found = 0
        for table in expected_tables:
            if table.upper() in sql_upper:
                tables_found += 1
        
        if expected_tables:
            match_score += 0.5 * (tables_found / len(expected_tables))
        
        # Check if expected operations are present
        expected_ops = test_case.get('expected_operations', [])
        ops_found = 0
        for op in expected_ops:
            if op.upper() in sql_upper:
                ops_found += 1
        
        if expected_ops:
            match_score += 0.5 * (ops_found / len(expected_ops))
        
        return min(match_score, 1.0)
    
    def _calculate_query_quality(self, sql: str, execution_time: float, test_case: Dict[str, Any]) -> float:
        """Calculate query quality score."""
        if not sql:
            return 0.0
        
        quality_score = 0.0
        
        # Valid SQL structure (20%)
        try:
            import sqlparse
            parsed = sqlparse.parse(sql)
            if parsed and len(parsed) > 0:
                quality_score += 0.2
        except:
            pass
        
        # Execution time (30%)
        complexity = test_case.get('complexity', 1)
        time_threshold = complexity * 1.0  # More lenient for complex queries
        
        if execution_time < time_threshold:
            quality_score += 0.3
        elif execution_time < time_threshold * 2:
            quality_score += 0.15
        
        # Query structure quality (50%)
        sql_upper = sql.upper()
        
        # Uses appropriate SELECT
        if 'SELECT' in sql_upper:
            quality_score += 0.1
        
        # Uses JOINs when needed for multi-table queries
        expected_tables = test_case.get('expected_tables', [])
        if len(expected_tables) > 1:
            if 'JOIN' in sql_upper:
                quality_score += 0.2
        else:
            quality_score += 0.2  # Single table queries don't need JOINs
        
        # Uses appropriate aggregation
        expected_ops = test_case.get('expected_operations', [])
        agg_ops = ['COUNT', 'SUM', 'AVG', 'MIN', 'MAX']
        if any(op in expected_ops for op in agg_ops):
            if any(op in sql_upper for op in agg_ops):
                quality_score += 0.1
        else:
            quality_score += 0.1
        
        # Uses GROUP BY when needed
        if 'GROUP BY' in expected_ops:
            if 'GROUP BY' in sql_upper:
                quality_score += 0.1
        else:
            quality_score += 0.1
        
        return min(quality_score, 1.0)

    def _calculate_overall_metrics(self) -> Dict[str, Any]:
        """Calculate overall system metrics."""
        if not self.results:
            return {}

        successful_results = [r for r in self.results if r['success']]

        return {
            'total_queries': len(self.results),
            'successful_queries': len(successful_results),
            'overall_success_rate': len(successful_results) / len(self.results),
            'average_accuracy_score': sum(r['accuracy_score'] for r in successful_results) / len(successful_results) if successful_results else 0.0,
            'average_execution_time': sum(r['execution_time'] for r in successful_results) / len(successful_results) if successful_results else 0.0,
            'queries_under_1s': len([r for r in successful_results if r['execution_time'] < 1.0]),
            'queries_over_2s': len([r for r in successful_results if r['execution_time'] > 2.0])
        }

    def _analyze_failed_queries(self) -> List[Dict[str, Any]]:
        """Analyze failed queries for patterns."""
        failed_queries = [r for r in self.results if not r['success']]

        analysis = []
        for failed in failed_queries:
            analysis.append({
                'question': failed['question'],
                'category': failed['category'],
                'error': failed['error'],
                'accuracy_score': failed['accuracy_score']
            })

        return analysis

    def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze performance metrics."""
        successful_results = [r for r in self.results if r['success']]

        if not successful_results:
            return {}

        execution_times = [r['execution_time'] for r in successful_results]

        return {
            'min_execution_time': min(execution_times),
            'max_execution_time': max(execution_times),
            'median_execution_time': sorted(execution_times)[len(execution_times)//2],
            'p95_execution_time': sorted(execution_times)[int(len(execution_times)*0.95)],
            'cache_hit_rate': self.text2sql_engine.get_cache_info()['hit_rate']
        }

    def _generate_recommendations(self, evaluation_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on evaluation results."""
        recommendations = []

        overall_metrics = evaluation_results['overall_metrics']

        # Success rate recommendations
        if overall_metrics.get('overall_success_rate', 0) < 0.9:
            recommendations.append("Improve query generation accuracy - success rate below 90%")

        # Accuracy score recommendations
        if overall_metrics.get('average_accuracy_score', 0) < 0.8:
            recommendations.append("Enhance prompt engineering to improve accuracy scores")

        # Performance recommendations
        if overall_metrics.get('average_execution_time', 0) > 1.5:
            recommendations.append("Optimize query execution performance - average time above 1.5s")

        # Category-specific recommendations
        for category, results in evaluation_results['results_by_category'].items():
            if results.get('success_rate', 0) < 0.8:
                recommendations.append(f"Focus on improving {category} query handling")

        # Failed query analysis
        failed_queries = evaluation_results['failed_queries']
        if len(failed_queries) > 0:
            error_types = {}
            for failed in failed_queries:
                error_type = failed['error'].split(':')[0] if failed['error'] else 'Unknown'
                error_types[error_type] = error_types.get(error_type, 0) + 1

            most_common_error = max(error_types.items(), key=lambda x: x[1])
            recommendations.append(f"Address most common error type: {most_common_error[0]}")

        if not recommendations:
            recommendations.append("System performance is excellent - consider advanced features")

        return recommendations

    def _save_evaluation_results(self, results: Dict[str, Any]) -> None:
        """Save evaluation results to files."""
        # Save JSON results
        results_file = 'evaluation_results.json'
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"Evaluation results saved to {results_file}")

        # Generate and save markdown report
        self._generate_markdown_report(results)

        # Generate performance plots
        self._generate_performance_plots()

    def _generate_markdown_report(self, results: Dict[str, Any]) -> None:
        """Generate markdown evaluation report."""
        report_lines = [
            "# Text2SQL Analytics System - Evaluation Report",
            "",
            f"**Generated:** {results['timestamp']}",
            f"**Total Test Cases:** {results['total_test_cases']}",
            "",
            "## Executive Summary",
            "",
            f"- **Overall Success Rate:** {results['overall_metrics'].get('overall_success_rate', 0):.1%}",
            f"- **Average Accuracy Score:** {results['overall_metrics'].get('average_accuracy_score', 0):.3f}",
            f"- **Average Execution Time:** {results['overall_metrics'].get('average_execution_time', 0):.3f}s",
            "",
            "## Results by Category",
            ""
        ]

        for category, category_results in results['results_by_category'].items():
            report_lines.extend([
                f"### {category.title()} Queries",
                "",
                f"- **Test Cases:** {category_results['total_cases']}",
                f"- **Success Rate:** {category_results['success_rate']:.1%}",
                f"- **Average Accuracy:** {category_results['average_accuracy']:.3f}",
                f"- **Average Execution Time:** {category_results['average_execution_time']:.3f}s",
                ""
            ])

        # Performance metrics
        perf_metrics = results.get('performance_metrics', {})
        if perf_metrics:
            report_lines.extend([
                "## Performance Analysis",
                "",
                f"- **Fastest Query:** {perf_metrics.get('min_execution_time', 0):.3f}s",
                f"- **Slowest Query:** {perf_metrics.get('max_execution_time', 0):.3f}s",
                f"- **Median Time:** {perf_metrics.get('median_execution_time', 0):.3f}s",
                f"- **95th Percentile:** {perf_metrics.get('p95_execution_time', 0):.3f}s",
                f"- **Cache Hit Rate:** {perf_metrics.get('cache_hit_rate', 0):.1%}",
                ""
            ])

        # Failed queries
        failed_queries = results.get('failed_queries', [])
        if failed_queries:
            report_lines.extend([
                "## Failed Queries Analysis",
                "",
                f"**Total Failed Queries:** {len(failed_queries)}",
                ""
            ])

            for failed in failed_queries[:5]:  # Show top 5 failures
                report_lines.extend([
                    f"- **Question:** {failed['question']}",
                    f"  - **Category:** {failed['category']}",
                    f"  - **Error:** {failed['error']}",
                    ""
                ])

        # Recommendations
        recommendations = results.get('recommendations', [])
        if recommendations:
            report_lines.extend([
                "## Recommendations",
                ""
            ])

            for i, rec in enumerate(recommendations, 1):
                report_lines.append(f"{i}. {rec}")

            report_lines.append("")

        # Save report
        with open('EVALUATION.md', 'w') as f:
            f.write('\n'.join(report_lines))

        logger.info("Evaluation report saved to EVALUATION.md")

    def _generate_performance_plots(self) -> None:
        """Generate performance visualization plots."""
        if not self.results:
            return

        try:
            # Create plots directory
            os.makedirs('plots', exist_ok=True)

            # Execution time histogram
            successful_results = [r for r in self.results if r['success']]
            execution_times = [r['execution_time'] for r in successful_results]

            plt.figure(figsize=(10, 6))
            plt.hist(execution_times, bins=20, alpha=0.7, edgecolor='black')
            plt.xlabel('Execution Time (seconds)')
            plt.ylabel('Frequency')
            plt.title('Query Execution Time Distribution')
            plt.grid(True, alpha=0.3)
            plt.savefig('plots/execution_time_histogram.png', dpi=300, bbox_inches='tight')
            plt.close()

            # Accuracy by category
            categories = []
            accuracies = []

            for result in successful_results:
                categories.append(result['category'])
                accuracies.append(result['accuracy_score'])

            df = pd.DataFrame({'Category': categories, 'Accuracy': accuracies})

            plt.figure(figsize=(10, 6))
            sns.boxplot(data=df, x='Category', y='Accuracy')
            plt.title('Accuracy Scores by Query Category')
            plt.ylabel('Accuracy Score')
            plt.grid(True, alpha=0.3)
            plt.savefig('plots/accuracy_by_category.png', dpi=300, bbox_inches='tight')
            plt.close()

            logger.info("Performance plots saved to plots/ directory")

        except Exception as e:
            logger.warning(f"Could not generate plots: {str(e)}")


def main():
    """Main entry point for evaluation script."""
    try:
        evaluator = Text2SQLEvaluator()
        results = evaluator.run_evaluation()

        print("\n" + "="*60)
        print("TEXT2SQL EVALUATION RESULTS")
        print("="*60)
        print(f"Total Test Cases: {results['total_test_cases']}")
        print(f"Overall Success Rate: {results['overall_metrics'].get('overall_success_rate', 0):.1%}")
        print(f"Average Accuracy Score: {results['overall_metrics'].get('average_accuracy_score', 0):.3f}")
        print(f"Average Execution Time: {results['overall_metrics'].get('average_execution_time', 0):.3f}s")

        print("\nResults by Category:")
        for category, category_results in results['results_by_category'].items():
            print(f"  {category.title()}: {category_results['success_rate']:.1%} success, {category_results['average_accuracy']:.3f} accuracy")

        failed_count = len(results.get('failed_queries', []))
        if failed_count > 0:
            print(f"\nFailed Queries: {failed_count}")

        print(f"\nDetailed results saved to:")
        print("- evaluation_results.json")
        print("- EVALUATION.md")
        print("- plots/ directory")

        # Check if system meets requirements
        overall_accuracy = results['overall_metrics'].get('average_accuracy_score', 0)
        if overall_accuracy >= 0.8:
            print(f"\n✅ SYSTEM PASSES: Accuracy score {overall_accuracy:.3f} >= 0.8")
        else:
            print(f"\n❌ SYSTEM FAILS: Accuracy score {overall_accuracy:.3f} < 0.8")

    except Exception as e:
        print(f"\n❌ Evaluation failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
