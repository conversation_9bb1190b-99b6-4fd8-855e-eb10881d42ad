"""Test configuration and fixtures for Text2SQL Analytics System."""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import Mock, patch
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# Import modules to test
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config import Config
from src.database import DatabaseManager
from src.data_loader import DataLoader
from src.query_validator import QueryValidator
from src.text2sql_engine import Text2SQLEngine
from src.utils import QueryMetrics

# Disable logging during tests
logging.disable(logging.CRITICAL)


@pytest.fixture(scope="session")
def test_config():
    """Test configuration fixture."""
    return {
        'DB_URL': 'sqlite:///:memory:',
        'GEMINI_API_KEY': 'test_api_key',
        'QUERY_TIMEOUT': 5,
        'MAX_ROWS': 100
    }


@pytest.fixture
def mock_config(test_config, monkeypatch):
    """Mock configuration for testing."""
    for key, value in test_config.items():
        monkeypatch.setattr(Config, key, value)
    
    # Mock methods
    monkeypatch.setattr(Config, 'validate_config', lambda: True)
    monkeypatch.setattr(Config, 'get_query_url', lambda: test_config['DB_URL'])
    
    return Config


@pytest.fixture
def sample_excel_data():
    """Sample Excel data for testing."""
    return {
        'categories': pd.DataFrame({
            'categoryid': [1, 2, 3],
            'categoryname': ['Beverages', 'Condiments', 'Dairy Products'],
            'description': ['Soft drinks, coffees, teas', 'Sweet and savory sauces', 'Cheeses']
        }),
        'customers': pd.DataFrame({
            'customerid': ['ALFKI', 'ANATR', 'ANTON'],
            'companyname': ['Alfreds Futterkiste', 'Ana Trujillo Emparedados', 'Antonio Moreno Taquería'],
            'contactname': ['Maria Anders', 'Ana Trujillo', 'Antonio Moreno'],
            'city': ['Berlin', 'México D.F.', 'México D.F.'],
            'country': ['Germany', 'Mexico', 'Mexico']
        }),
        'products': pd.DataFrame({
            'productid': [1, 2, 3],
            'productname': ['Chai', 'Chang', 'Aniseed Syrup'],
            'categoryid': [1, 1, 2],
            'unitprice': [18.0, 19.0, 10.0],
            'unitsinstock': [39, 17, 13],
            'discontinued': [False, False, False]
        }),
        'orders': pd.DataFrame({
            'orderid': [10248, 10249, 10250],
            'customerid': ['ALFKI', 'ANATR', 'ANTON'],
            'orderdate': pd.to_datetime(['1996-07-04', '1996-07-05', '1996-07-08']),
            'freight': [32.38, 11.61, 65.83]
        }),
        'order_details': pd.DataFrame({
            'orderid': [10248, 10248, 10249],
            'productid': [1, 2, 3],
            'unitprice': [18.0, 19.0, 10.0],
            'quantity': [12, 10, 5],
            'discount': [0.0, 0.0, 0.0]
        })
    }


@pytest.fixture
def temp_excel_file(sample_excel_data):
    """Create temporary Excel file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        with pd.ExcelWriter(tmp_file.name, engine='openpyxl') as writer:
            for sheet_name, df in sample_excel_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        yield tmp_file.name
    
    # Cleanup
    os.unlink(tmp_file.name)


@pytest.fixture
def mock_database_manager(mock_config):
    """Mock database manager for testing."""
    with patch('src.database.create_engine') as mock_engine:
        # Create in-memory SQLite engine for testing
        engine = create_engine('sqlite:///:memory:', echo=False)
        mock_engine.return_value = engine
        
        db_manager = DatabaseManager(use_query_user=False)
        db_manager.admin_engine = engine
        db_manager.query_engine = engine
        
        # Create test tables
        with engine.connect() as conn:
            conn.execute(text("""
                CREATE TABLE customers (
                    customerid TEXT PRIMARY KEY,
                    companyname TEXT,
                    contactname TEXT,
                    city TEXT,
                    country TEXT
                )
            """))
            
            conn.execute(text("""
                CREATE TABLE products (
                    productid INTEGER PRIMARY KEY,
                    productname TEXT,
                    categoryid INTEGER,
                    unitprice REAL,
                    unitsinstock INTEGER,
                    discontinued BOOLEAN
                )
            """))
            
            conn.execute(text("""
                CREATE TABLE orders (
                    orderid INTEGER PRIMARY KEY,
                    customerid TEXT,
                    orderdate DATE,
                    freight REAL
                )
            """))
            
            # Insert test data
            conn.execute(text("""
                INSERT INTO customers VALUES 
                ('ALFKI', 'Alfreds Futterkiste', 'Maria Anders', 'Berlin', 'Germany'),
                ('ANATR', 'Ana Trujillo Emparedados', 'Ana Trujillo', 'México D.F.', 'Mexico')
            """))
            
            conn.execute(text("""
                INSERT INTO products VALUES 
                (1, 'Chai', 1, 18.0, 39, 0),
                (2, 'Chang', 1, 19.0, 17, 0)
            """))
            
            conn.execute(text("""
                INSERT INTO orders VALUES 
                (10248, 'ALFKI', '1996-07-04', 32.38),
                (10249, 'ANATR', '1996-07-05', 11.61)
            """))
            
            conn.commit()
        
        yield db_manager


@pytest.fixture
def data_loader(temp_excel_file):
    """Data loader fixture with test data."""
    loader = DataLoader(temp_excel_file)
    return loader


@pytest.fixture
def query_validator():
    """Query validator fixture."""
    return QueryValidator()


@pytest.fixture
def mock_gemini_response():
    """Mock Gemini API response."""
    mock_response = Mock()
    mock_response.text = "SELECT * FROM customers WHERE country = 'Germany'"
    return mock_response


@pytest.fixture
def mock_text2sql_engine(mock_database_manager, mock_gemini_response):
    """Mock Text2SQL engine for testing."""
    with patch('google.generativeai.configure'), \
         patch('google.generativeai.GenerativeModel') as mock_model_class:
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_gemini_response
        mock_model_class.return_value = mock_model
        
        engine = Text2SQLEngine(mock_database_manager)
        yield engine


@pytest.fixture
def query_metrics():
    """Query metrics fixture."""
    return QueryMetrics()


@pytest.fixture
def sample_queries():
    """Sample queries for testing."""
    return [
        {
            'question': 'Show all customers',
            'expected_sql': 'SELECT * FROM customers',
            'should_succeed': True
        },
        {
            'question': 'Delete all customers',
            'expected_sql': 'DELETE FROM customers',
            'should_succeed': False
        },
        {
            'question': 'Show customers from Germany',
            'expected_sql': "SELECT * FROM customers WHERE country = 'Germany'",
            'should_succeed': True
        },
        {
            'question': 'Count total orders',
            'expected_sql': 'SELECT COUNT(*) FROM orders',
            'should_succeed': True
        }
    ]


@pytest.fixture
def accuracy_test_cases():
    """Test cases for accuracy testing."""
    return [
        # Simple queries
        {
            'category': 'simple',
            'question': 'Show all customers',
            'expected_sql': 'SELECT * FROM customers',
            'expected_tables': ['customers'],
            'complexity': 1
        },
        {
            'category': 'simple',
            'question': 'List all products',
            'expected_sql': 'SELECT * FROM products',
            'expected_tables': ['products'],
            'complexity': 1
        },
        {
            'category': 'simple',
            'question': 'Show orders',
            'expected_sql': 'SELECT * FROM orders',
            'expected_tables': ['orders'],
            'complexity': 1
        },
        
        # Intermediate queries
        {
            'category': 'intermediate',
            'question': 'Show customers from Germany',
            'expected_sql': "SELECT * FROM customers WHERE country = 'Germany'",
            'expected_tables': ['customers'],
            'complexity': 2
        },
        {
            'category': 'intermediate',
            'question': 'Count total customers',
            'expected_sql': 'SELECT COUNT(*) FROM customers',
            'expected_tables': ['customers'],
            'complexity': 2
        },
        {
            'category': 'intermediate',
            'question': 'Show products with price greater than 15',
            'expected_sql': 'SELECT * FROM products WHERE unitprice > 15',
            'expected_tables': ['products'],
            'complexity': 2
        },
        
        # Complex queries
        {
            'category': 'complex',
            'question': 'Show customer names and their order counts',
            'expected_sql': '''SELECT c.companyname, COUNT(o.orderid) 
                              FROM customers c 
                              LEFT JOIN orders o ON c.customerid = o.customerid 
                              GROUP BY c.companyname''',
            'expected_tables': ['customers', 'orders'],
            'complexity': 3
        },
        {
            'category': 'complex',
            'question': 'Show top 5 most expensive products',
            'expected_sql': 'SELECT * FROM products ORDER BY unitprice DESC LIMIT 5',
            'expected_tables': ['products'],
            'complexity': 3
        }
    ]


@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test."""
    yield
    # Any cleanup code here


# Test utilities
def assert_dataframe_equal(df1, df2, check_dtype=False):
    """Assert that two DataFrames are equal."""
    pd.testing.assert_frame_equal(df1, df2, check_dtype=check_dtype)


def assert_sql_equivalent(sql1, sql2):
    """Assert that two SQL queries are functionally equivalent."""
    # Simple normalization for testing
    def normalize_sql(sql):
        return ' '.join(sql.upper().split())
    
    assert normalize_sql(sql1) == normalize_sql(sql2)


# Mock data generators
def generate_mock_customers(count=10):
    """Generate mock customer data."""
    return pd.DataFrame({
        'customerid': [f'CUST{i:03d}' for i in range(count)],
        'companyname': [f'Company {i}' for i in range(count)],
        'contactname': [f'Contact {i}' for i in range(count)],
        'city': ['Berlin', 'London', 'Paris', 'Madrid', 'Rome'] * (count // 5 + 1),
        'country': ['Germany', 'UK', 'France', 'Spain', 'Italy'] * (count // 5 + 1)
    })[:count]


def generate_mock_products(count=20):
    """Generate mock product data."""
    return pd.DataFrame({
        'productid': range(1, count + 1),
        'productname': [f'Product {i}' for i in range(1, count + 1)],
        'categoryid': [i % 5 + 1 for i in range(count)],
        'unitprice': [10.0 + i * 2.5 for i in range(count)],
        'unitsinstock': [50 - i for i in range(count)],
        'discontinued': [i % 10 == 0 for i in range(count)]
    })
