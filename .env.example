# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=text2sql_analytics
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_URL=postgresql://postgres:your_password_here@localhost:5432/text2sql_analytics

# Read-only database user for query execution
DB_QUERY_USER=query_user
DB_QUERY_PASSWORD=query_password_here

# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyAyZDblnb9R2TD66E0VpY2X7mM5HwmoSWA

# Application Configuration
LOG_LEVEL=INFO
QUERY_TIMEOUT=5
MAX_ROWS=1000

# Environment
ENVIRONMENT=development
